<?php

namespace addons\weipinshang\library;

use think\Hook;

class WPConfigUtil
{
    public $name;
    public $channel;
    public $appkey;
    public $url;
    public $notifyUrl;
    public $shop_id;
    public $percent;
    public $globalUrl;

    public function __construct()
    {
        $result = Hook::exec('addons\weipinshang\Weipinshang', 'getConfig');
        $switch = $result['switch'];

        list('name' => $this->name, 'shop_id' => $this->shop_id, 'channel' => $this->channel,
            'appkey' => $this->appkey, 'percent' => $this->percent, 'globalUrl' => $this->globalUrl,
            'url' => $this->url, 'notifyUrl' => $this->notifyUrl) = $result[$switch];
    }

    public function apiRest($apiRest, $params)
    {
        $restUrl = $this->url.$apiRest;
        $headers = [
            'channelType:' . $this->channel,
            'md5:' . $this->sign($params)
        ];
        $resp = $this->request($restUrl, $params, $headers);
        return json_decode($resp, 1);
    }

    public function sign($queries = [])
    {
        $query = [];
        ksort($queries);
        foreach ($queries as $key => $val) {
            $query[] = $key . '=' . $val;
        }
        $data = implode('&', $query);
        $md5 = strtolower(md5($data . $this->channel . $this->appkey));
        return $md5;
    }

    //参数1：访问的URL，参数2：post数据(不填则为GET)，参数3：提交的$cookies,参数4：是否返回$cookies
    public function request($url, $post = NULL, $headers = [])
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        //设置头文件的信息作为数据流输出
        //curl_setopt($curl, CURLOPT_HEADER, 0);
        //设置获取的信息以文件流的形式返回，而不是直接输出。
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        if (!empty($post)) {
            curl_setopt($curl, CURLOPT_POST, 1);
            curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($post));
        }
        $data = curl_exec($curl);
        if (curl_errno($curl)) {
            return curl_error($curl);
        }
        curl_close($curl);
        return $data;
    }
}