<?php

return [
    [
        'name' => 'test',
        'title' => '测试参数',
        'type' => 'array',
        'value' => [
            'name' => 'test',
            'appid' => '10037e6f803bc8ab01805fd07db4000d',
            'appkey' => '18a3a8d41b9f44d9acdf39c360f7a971',
            'mid' => '898201612345678',
            'tid' => '00000001',
            'msgSrcId' => '103A',
            'secret' => 'GAhPWQ8D4hXanneneaydaHYHiwn64p7y3A46Jpss87aKWsy5',
            'url' => 'https://test-api-open.chinaums.com/v1',
            'notifyUrl' => 'https://aidomino.jfb365.com/addons/ningbopay/index/notify',
            'showUrl' => 'https://aidomino.jfb365.com/addons/ningbopay/index/orderInfo',
        ],
        'rule' => '',
        'msg' => '',
        'tip' => '测试参数配置',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name' => 'dist',
        'title' => '正式参数',
        'type' => 'array',
        'value' => [
            'name' => 'dist',
            'appid' => '10037e6f803bc8ab01805fd07db4000d',
            'appkey' => '18a3a8d41b9f44d9acdf39c360f7a971',
            'mid' => '898201612345678',
            'tid' => '00000001',
            'msgSrcId' => '103A',
            'secret' => 'GAhPWQ8D4hXanneneaydaHYHiwn64p7y3A46Jpss87aKWsy5',
            'url' => 'https://api-mop.chinaums.com/v1',
            'notifyUrl' => 'https://aidomino.jfb365.com/addons/ningbopay/index/notify',
            'showUrl' => 'https://aidomino.jfb365.com/addons/ningbopay/index/orderInfo',
        ],
        'rule' => '',
        'msg' => '',
        'tip' => '测试参数配置',
        'ok' => '',
        'extend' => '',
    ],
    [
        'name'    => 'switch',
        'title'   => '环境切换 dist/test',
        'type'    => 'string',
        'content' => [
        ],
        'value'   => 'test',
        'rule'    => 'required',
        'msg'     => '',
        'tip'     => '开关: dist/test',
        'ok'      => '',
        'extend'  => ''
    ],
];
