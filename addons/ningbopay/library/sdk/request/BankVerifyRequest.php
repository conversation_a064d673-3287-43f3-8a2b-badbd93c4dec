<?php
require_once '../utils/Log.php';
/**
 *
 * 类名:银行卡实名认证请求类
 * 功能:提供请求数据
 * 版本:1.0
 * 日期:2017-6-20
 * 作者:张埔枘
 * 版权:张埔枘
 * 说明:以下代码只是为了方便商户测试而提供的样例代码，商户可以根据自己的需要，按照技术文档编写，并非一定要使用该代码。该代码仅供参考
 */
class BankVerifyRequest{
    public $url = '';
    public $serviceCode = '/datacenter/smartverification/bankcard/verify';
    public $apiVersion = 'v1';
    public $apiMethodName = '银行卡实名认证';
    public $needToken = true;
    public $needDataTag = true;
    public $data;
    function __construct($data,$url) {
        $this->data=$data;
        $this->url=$url;
        Log::outLog('银行卡实名认证','请求数据初始化');
    }
}