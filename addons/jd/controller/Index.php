<?php

namespace addons\jd\controller;

use addons\jd\library\GoodsService;
use addons\jd\library\OrderService;
use addons\jd\library\ToolService;
use addons\jd\packet\JdGoods;
use app\admin\model\User;
use app\admin\model\wanlshop\OrderUnion;
use think\addons\Controller;
use think\queue\connector\Redis;

class Index extends Controller
{
    protected $responseType = 'json';

    public function index()
    {
        $this->error("当前插件暂无前台页面");
    }

    public function queryOrder()
    {
        $order = (new OrderService())->queryOrder($this->request->param());
        var_dump($order['data']);
        if($order['code'] == 200){
            $state = [
                16=>1,
                24=>1,
                17=>2,
                25=>7,
                26=>7,
                27=>7,
                28=>7,
                13=>7,
            ];
            $flag = false;
            if(!array_get($order, 'data')){
                var_dump($flag);exit;
            }
            foreach ($order['data'] as $v){
                //先查询订单是否已存在，状态是否对应
                $info = OrderUnion::where('order_no',$v['orderId'])->find();
                $status = array_get($state, $v['validCode'],5); //其他均为已取消
                //判断是否有结算时间，有，且是过去时间则为已结算
                if($v['payMonth'] && $v['payMonth'] < date('Ymd')){
                    $status = 6;//已结算
                }
                if(!$info || $info['state'] != $status){
                    //查询用户信息
                    $user_id = $v['subUnionId'];
                    $user_info['saas_id'] = 0;
                    if($user_id){
                        $user_info = User::where('id',$user_id)->find();
                    }
                    $id = array_get($info, 'id','');
                    $orderArr = [
                        'id'=>$id,
                        'platform'=>'jd',
                        'user_id'=>$user_id,
                        'saas_id'=>$user_info['saas_id'],
                        'order_no'=>$v['orderId'],
                        'goods_id'=>$v['itemId'],
                        'goods_name'=>$v['skuName'],
                        'goods_thumbnail_url'=>array_get($v['goodsInfo'], 'imageUrl'),
                        'goods_num'=>$v['skuNum'],
                        'refund_num'=>$v['skuReturnNum'],
                        'price'=>$v['estimateCosPrice']*$v['skuNum'],
                        'pid'=>$v['positionId'],
                        'resp_json'=>json_encode($v),
                        'state'=>$status,
                        'remarks'=>'',
                        'pay_time'=>$v['orderTime'],
                        'union_settle_time'=>$v['payMonth'],
                        'receive_time'=>$v['finishTime'],
                        'platform_commission'=>$v['actualFee'],
                        'commission'=>$v['actualFee']*0.28,
                    ];
                    $where = [];
                    $id && $where = ['id','=',$id];
                    if(model('\app\admin\model\wanlshop\OrderUnion')->save($orderArr,$where)){
                        $flag = true;
                    }
                }
            }
            var_dump($flag);
        }

    }

    public function play()
    {
        $param = array_merge(['eliteId'=>2,'pageIndex'=>1,'pageSize'=>10],array_filter($this->request->param()));
        $rsp = (new GoodsService())->queryJfGoods($param);
        $this->assign('param',$param);
        $this->assign('data',$rsp['data']);
        $this->assign('eliteId',JdGoods::eliteId());
        return $this->fetch('play');
    }

    public function toLink()
    {
        $rsp = (new GoodsService())->getPromotion(array_merge($this->request->only('materialId,positionId,couponUrl'), ['subUnionId'=>'phpzhs666','ext1'=>'phpzhs6668']));
        if(isset($rsp['data']['clickURL'])){
            header('Location:'.$rsp['data']['clickURL']);
        }
        var_dump($rsp);
    }

    // public function createPosition()
    // {
    //     $rsp = (new GoodsService())->createPosition($this->request->post());
    //     $this->result($rsp,$rsp['code'],$rsp['message'],$this->responseType);
    // }

    // public function queryGoodsDetails()
    // {
    //     $rsp = (new GoodsService())->queryGoodsDetails($this->request->post());
    //     $this->result($rsp,$rsp['code'],$rsp['message'],$this->responseType);
    // }
    //
    // public function queryGoods(){
    //     $rsp = (new GoodsService('8e445339a43a49d3af325bc03587d281zmzl'))->queryGoods($this->request->post());
    //     $this->result($rsp,$rsp['code'],$rsp['message'],$this->responseType);
    // }
    //
    // public function queryJfGoods()
    // {
    //     $rsp = (new GoodsService())->queryJfGoods($this->request->post());
    //     $this->result($rsp,$rsp['code'],$rsp['message'],$this->responseType);
    // }
    //
    // /**
    //  * 猜你喜欢商品推荐
    //  * @return void
    //  */
    // public function queryMaterial()
    // {
    //     $rsp = (new GoodsService())->queryMaterial($this->request->post());
    //     $this->result($rsp,$rsp['code'],$rsp['message'],$this->responseType);
    // }
    //
    // public function getGoodsCategory()
    // {
    //     $rsp = (new GoodsService())->getGoodsCategory($this->request->post());
    //     $this->result($rsp,$rsp['code'],$rsp['message'],$this->responseType);
    // }
    //
    // public function getPromotion()
    // {
    //     $rsp = (new GoodsService())->getPromotion($this->request->post());
    //     $this->result($rsp,$rsp['code'],$rsp['message'],$this->responseType);
    // }
    //
    // public function getCodeUrl()
    // {
    //     // for($i = 0; $i < 10; $i++) {
    //     //     var_dump((new Redis([]))->push('phpzhs',['order_no'=>date('Y-m-d H:i:s'),'id'=>rand(1,100)],'order'));
    //     // }die();
    //     // $pop = (new Redis([]))->pop('order');
    //     // while ($pop){
    //     //     $data =  $pop->getRawBody();
    //     //     $data = json_decode($data,true);
    //     //     var_dump($data);
    //     //     if(true){//处理失败
    //     //         //删除
    //     //         $pop->delete();
    //     //     }
    //     //     //继续取队列数据
    //     //     $pop = (new Redis([]))->pop('order');
    //     // }
    //     var_dump((new ToolService())->getCodeUrl());
    // }

}
