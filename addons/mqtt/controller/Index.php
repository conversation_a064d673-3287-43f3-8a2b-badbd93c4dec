<?php

namespace addons\mqtt\controller;

use think\addons\Controller;
use Bluer<PERSON><PERSON>\phpMQTT;
use think\Hook;
use Mos<PERSON>tto\Client;

class Index extends Controller
{

    public function index()
    {
        $params = [
            'action'=>'sendMsg',
            'data'=>[
                'client_id'=>'U25012110110861',
                'msg'=>'银联收款1元',
            ],
        ];
        var_dump(Hook::listen('mqtt',$params));die();
        $this->error("当前插件暂无前台页面");
    }

    /**
     */
    public function test(){
        $config = [
            'server'=>'mqtt.aliyuncs.com',// MQTT服务器地址
            'port'=>1883,// MQTT服务器端口
            'access_id'=>'LTAI5tAoAqNYD4sopw7qx1VE',// MQTT服务器端口
            'secret_key'=>'******************************',// MQTT服务器端口
            'instance_id'=>'post-cn-cfn43k6dk03',// MQTT服务器端口
            'group_id'=>'GID_test',// 分组
            'topic'=>'test',//主题
        ];
        $client_id = $config['group_id'].'@@@U25012110111231';
        // $server = 'post-cn-cfn43k6dk03.mqtt.aliyuncs.com';    // MQTT服务器地址
        // $port = 1883;             // MQTT服务器端口
        $username = 'Signature|' . $config['access_id'] . '|' . $config['instance_id'];
        $sigStr = hash_hmac("sha1", $client_id, $config['secret_key'], true);
        $password = base64_encode($sigStr);
        // echo "UserName: {$username} \r\nPassword: {$password}", PHP_EOL;
        $message = json_encode([
            'cmd'=>'voice',
            'msg'=>'支付宝收款'.rand(1,9999).'元',
        ]); // 要发送的消息

        $mqtt = new phpMQTT($config['instance_id'].'.'.$config['server'], $config['port'], $client_id);
        if ($mqtt->connect(true, NULL, $username, $password)) {
            $mqtt->publish($config['topic'], $message, 0); // 发布消息
            $mqtt->close();
            echo "Message published.\n";
        } else {
            echo "Failed to connect.\n";
        }
    }


    public function zzz()  {
        $config = [
            'server'=>'mqtt.aliyuncs.com',// MQTT服务器地址
            'port'=>1883,// MQTT服务器端口
            'access_id'=>'LTAI5tAoAqNYD4sopw7qx1VE',// MQTT服务器端口
            'secret_key'=>'******************************',// MQTT服务器端口
            'instance_id'=>'post-cn-cfn43k6dk03',// MQTT服务器端口
            'group_id'=>'GID_test',// 分组
            'topic'=>'test',//主题
        ];
        $client_id = $config['group_id'].'@@@U2501211011';
        $message1 = json_encode([
            'cmd'=>'voice',
            'msg'=>'支付宝收款'.rand(1,9999).'元',
        ]); // 要发送的消息
        $accessKey = 'LTAI5tAoAqNYD4sopw7qx1VE';
        ##此处填写阿里云帐号 SecretKey
        $secretKey = '******************************';
        ## 接入点地址，购买实例后从控制台获取
        $endPoint = 'post-cn-cfn43k6dk03.mqtt.aliyuncs.com';
        ##实例 ID，购买后从控制台获取
        $instanceId = 'post-cn-cfn43k6dk03';
        ## MQTT 第一级 Topic 需要在 MQTT 控制台提前申请
        $topic = 'test';
        ## MQTT 客户端ID 前缀， GroupID，需要在 MQTT 控制台申请
        $groupId = 'GID_test';
        ## MQTT 客户端ID 后缀，DeviceId，业务方自由指定，需要保证全局唯一，禁止 2 个客户端连接使用同一个 ID
        $deviceId = 'U2501211011';
        $qos = 0;
        $port = 1883;
        $keepalive = 90;
        $cleanSession = true;
        $clientId = $groupId . '@@@' . $deviceId;
        echo $clientId . "\n";

        $mid = 0;
        ## 初始化客户端，需要设置 clientId 和 CleanSession 参数，参考官网文档规范
        $mqttClient = new Client($clientId, $cleanSession);


        ## 设置鉴权参数，参考 MQTT 客户端鉴权代码计算 username 和 password
        $username = 'Signature|' . $accessKey . '|' . $instanceId;
        $sigStr = hash_hmac("sha1", $clientId, $secretKey, true);
        $password = base64_encode($sigStr);
        echo "UserName:" . $username . "  Password:" . $password . "\n";
        $mqttClient->setCredentials($username, $password);

        ## 设置连接成功回调
        $mqttClient->onConnect(function ($rc, $message) use ($mqttClient, &$mid, $topic, $qos, $clientId,$message1) {
            echo "Connnect to Server Code is " . $rc . " message is " . $message . "\n";
            ## P2P消息的二级 topic 是/p2p/,三级 topic 是目标客户端的 clientId
            $mqttP2PTopic = $topic . "/p2p/" . $clientId;
            $mqttClient->publish($mqttP2PTopic,$message1, $qos);
        });

        ## 设置发送成功回调
        $mqttClient->onPublish(function ($publishedId) use ($mqttClient, $mid) {
            echo "publish message success " . $mid . "\n";
        });


        ## 设置消息接收回调
        $mqttClient->onMessage(function ($message) {
            echo "Receive Message From mqtt, topic is " . $message->topic . "  qos is " . $message->qos . "  messageId is " . $message->mid . "  payload is " . $message->payload . "\n";

        });
        $mqttClient->connect($endPoint, $port, $keepalive);


        $mqttClient->loopForever();

        echo "Finished";
        
    }
}
