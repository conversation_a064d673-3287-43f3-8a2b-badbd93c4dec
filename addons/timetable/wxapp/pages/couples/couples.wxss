.headBox {
  width: 750rpx; }
  .headBox .backgroundTexture {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: -1;
    background: #ff8771; }
  .headBox .action {
    font-size: 32rpx;
    font-weight: 500;
    line-height: 45rpx;
    color: #fff; }

page {
  background: #ffffff; }

.settingBox {
  margin-top: -65rpx;
  border-radius: 50rpx 50rpx 0 0;
  z-index: 10; }
  .settingBox .settitle {
    font-size: 36rpx;
    font-weight: 500;
    line-height: 50rpx;
    color: #282b2f; }
  .settingBox .messageHistory {
    margin: 40rpx 32rpx 0;
    overflow: auto; }
    .settingBox .messageHistory .content {
      margin: 24rpx 0;
      width: 686rpx;
      background: #f6f6f9;
      opacity: 1;
      border-radius: 24rpx;
      padding: 32rpx;
      word-break: break-all; }
  .settingBox .coursenull {
    display: block;
    margin: 60rpx auto;
    width: 360rpx;
    height: 360rpx; }
    .settingBox .coursenull text {
      display: block;
      margin: -50rpx auto 0;
      text-align: center;
      font-size: 28rpx;
      line-height: 40rpx;
      color: #92979d; }
  .settingBox .settingItem {
    width: 686rpx;
    height: 140rpx;
    background: #ffffff;
    box-shadow: 0 4rpx 18rpx rgba(181, 186, 193, 0.2);
    border-radius: 30rpx;
    margin: 24rpx auto 0; }
    .settingBox .settingItem .settingName {
      font-size: 36rpx;
      font-weight: 500;
      line-height: 50rpx;
      color: #282b2f; }

.contactInfo {
  width: 100%; }

.contactBind {
  font-size: 24rpx;
  font-weight: 400;
  line-height: 33rpx;
  color: #ffffff;
  opacity: 0.6;
  right: 0;
  margin: -38rpx -580rpx 0 0; }

.shareInvi {
  width: 686rpx;
  height: 280rpx;
  box-shadow: 0 4rpx 18rpx rgba(181, 186, 193, 0.2);
  border-radius: 24rpx; }
  .shareInvi .shareWechat {
    width: 120rpx;
    height: 120rpx; }

.shareInvi::after {
  border: 0; }

.message {
  width: 70rpx;
  height: 70rpx;
  margin-top: 20rpx; }

.title {
  font-size: 32rpx;
  font-weight: 500;
  line-height: 45rpx;
  color: #282b2f; }

.tips {
  font-size: 24rpx;
  font-weight: 400;
  line-height: 33rpx;
  color: #92979d; }

.removeIcon {
  width: 120rpx;
  height: 120rpx;
  background: #f6f6f9;
  border-radius: 50%;
  opacity: 1; }

.removeButton {
  width: 220rpx;
  height: 78rpx;
  background-color: #f6f6f9;
  border-radius: 39rpx;
  color: #282b2f; }

.org {
  width: 220rpx;
  height: 78rpx;
  border-radius: 39rpx;
  background: #ff8771;
  color: #fff; }
