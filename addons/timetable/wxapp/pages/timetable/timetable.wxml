<!-- * @Author: EvilStorm
 * @Date: 2020-10-26 10:44:42
 * @LastEditTime: 2020-10-26 17:16:46
 * @LastEditors: EvStorm <EMAIL>
 * @WebSite: https://www.lianshoulab.com/
 * @Description: 周课程表 -->
<wxs src="./timetable.wxs" module="tools" />
<view class="headBox">
    <view style="height:{{CustomBar}}px">
        <view class="cu-bar" style="height:{{CustomBar}}px;padding-top:{{StatusBar}}px;">
            <view class="action" bindtap="weeksSwitch">
                <view class="ev-fc-cstart">
                    <view class="weeks ev-fr-start">
                        第{{showWeek}}周
                        <text wx:if="{{currentWeek == showWeek}}">(本周)</text>
                        <text class="cuIcon-unfold" style="color: #1a1b1c;" />
                        <view wx:if="{{currentWeek !== showWeek}}" class="text-blue margin-left-xs" catch:tap="backToCurrent" style="font-size: 24rpx;">
                            回到当前周
                        </view>
                    </view>
                    <view class="switchCouples">
                        {{gradeList[userInfo.grade].label}} 第{{semester}}学期
                    </view>
                </view>
            </view>
        </view>
    </view>
</view>
<view class="ev-relative" style="height: {{displayArea.windowWidth / 3.75}}px">
    <view class="flex solid-bottom padding align-center bg-white" bind:tap="switchCouples">
        <image class="ev-icon" mode="scaleToFill" src="/images/{{showcouples ? 'backTo.png':'couples.png'}}" />
        <text class="switchCouples margin-left-xs">{{showcouples ? "切换回我的课表":"切换情侣课表"}}</text>
    </view>
    <view class="ev-fr-start" style="box-shadow: 10px 10px 1px #e3e3e3;">
        <view class="monthBox ev-fc-center">
            10
            <view class="tipsText">月</view>
        </view>
        <view class="flex solid-bottom justify-between align-center">
            <view wx:for="{{['一','二','三','四','五','六','日']}}" wx:key="index" class="ev-fc calendarDate {{index == todayWeek && showWeek == currentWeek ? 'selectedDate':''}}">
                <text>周{{item}}</text>
                <text class="tipsText">{{weeksList[index]}}</text>
            </view>
        </view>
    </view>
</view>
<view>
    <view style="height:{{displayArea.windowHeight - CustomBar - (displayArea.windowWidth / 3.75)}}px;overflow: auto;" class="flex solid-bottom justify-start bg-white {{showcouples?'showcouplesOn':'showcouplesOut'}}">
        <image class="customBG" mode="aspectFill" style="width:100%;height:100%" src="{{userInfo.table_bgimage ? ImgUrl + userInfo.table_bgimage:''}}" />
        <block wx:if="{{!userInfo}}">
            <view class="loginnull">
                <view bindgetuserinfo="bindGetUserInfo" open-type="getUserInfo" style="width:100%;height:100%">
                    <image style="width:100%;height:100%" mode="scaleToFill" src="/images/loginnull.png" />
                </view>
            </view>
        </block>
        <view wx:elif="{{!courseInfo}}" class="coursenull" bind:tap="addTable">
            <image style="width:100%;height:100%" mode="scaleToFill" src="/images/coursenull.png" />
            <view class="ev-fc-start" style="margin-top:-20px">
                <view>没有课程或还未添加课程哦~</view>
                <view class="text-blue">去添加</view>
            </view>
        </view>
        <block wx:else>
            <view class="margin-top-sm">
                <view wx:for="{{classTime}}" wx:key="index" style="width:750rpx;margin-top:{{6 +(index)*50}}px;position:absolute;border-bottom:1px dashed #DFE4EA;z-index:-1"></view>
                <view wx:for="{{classTime}}" wx:key="index" class="ev-fc-start" style="height: 50px; width: 32px;z-index:10">
                    <text class="classTimeText">{{item.s_time}}</text>
                </view>
            </view>
            <view class="margin-top-sm" style="width:100%;margin-top:16px">
                <table-view dataSource="{{courseInfo}}" colorList="{{colorList}}" bind:onAdd="addTable" bind:onEdit="editTable"></table-view>
                <!-- <block>
                    <view wx:for="{{courseInfo}}" wx:key="index" class="flex-item kcb-item {{editTableId == item.subject_id ? 'kcb-selected':''}}" catch:tap="editTable" data-statu="open" data-id="{{item.subject_id}}" style="margin-left:{{(item.days-1)*98}}rpx;margin-top:{{(item.nums-1)*50+8}}px;height:{{(item.enum - item.nums + 1)*50-4}}px;background-color:{{colorList[item.subject_id%8]}};border: 1px solid {{colorList[item.subject_id%8]}};box-shadow:0px 3px 6px {{tools.hexToRgbA(colorList[item.subject_id%8])}};">
                        <view class="smalltext">{{item.sname}}</view>
                        <view class="room">@{{item.classroom}}</view>
                    </view>
                </block> -->
            </view>
        </block>
    </view>
    <block wx:if="{{showcouples}}">
        <view style="height:{{displayArea.windowHeight - CustomBar - (displayArea.windowWidth / 3.75)}}px;overflow: auto;margin-top:-{{displayArea.windowHeight - CustomBar - (displayArea.windowWidth / 3.75)}}px;width:100%;" class="flex solid-bottom justify-start bg-white {{showcouples?'showcouplesOut':'showcouplesOn'}}">
            <view wx:if="{{!loverCourse}}" class="coursenull">
                <image style="width:100%;height:100%" mode="scaleToFill" src="/images/coursenull.png" />
                <text>ta还没有课程~</text>
            </view>
            <block wx:else>
                <view class="margin-top-sm">
                    <view wx:for="{{classTime}}" wx:key="index" style="width:750rpx;margin-top:{{6 +(index)*50}}px;position:absolute;border-bottom:1px dashed #DFE4EA;"></view>
                    <view wx:for="{{classTime}}" wx:key="index" class="ev-fc-start" style="height: 50px; width: 32px;z-index:10">
                        <text class="classTimeText">{{item.s_time}}</text>
                    </view>
                </view>
                <view class="margin-top-sm" style="width:100%;margin-top:16px">
                    <table-view dataSource="{{loverCourse}}" disabled colorList="{{colorList}}"></table-view>
                    <!-- <view wx:for="{{loverCourse}}" wx:key="index" class="flex-item kcb-item" data-statu="open" data-id="{{item.subject_id}}" style="margin-left:{{(item.days-1)*98}}rpx;margin-top:{{(item.nums-1)*50+8}}px;height:{{(item.enum - item.nums + 1)*50-4}}px;background-color:{{colorList[item.subject_id%8]}};border: 1px solid {{colorList[item.subject_id%8]}};box-shadow:0px 3px 6px {{tools.hexToRgbA(colorList[item.subject_id%8])}};">
                        <view class="smalltext">{{item.sname}}</view>
                        <view class="room">@{{item.classroom}}</view>
                    </view> -->
                </view>
            </block>
        </view>
    </block>
</view>
<!-- 弹窗更改周数 -->
<view class="cu-modal top-modal weeksSwitch {{weeksSwitch ?'show':''}}" style="margin-top:{{CustomBar}}px" bind:tap="hideweeksSwitch">
    <view class="cu-dialog">
        <view class="grid col-5 padding-sm bg-white solid-top solid-bottom">
            <view wx:for="{{checkbox}}" class="padding-xs" wx:key="index">
                <button class="cu-btn bg-white WeekCheck tips {{showWeek == item.value?'WeekChecked':''}}" bindtap="ChooseCheckbox" data-value="{{item.value}}">
                    第
                    <view class="WeekNum">{{item.value}}</view>
                    周
                    <view class="thisWeek" style="color:{{showWeek == item.value ?'':'#92979D'}}" wx:if="{{currentWeek== item.value}}">
                        本周
                    </view>
                </button>
            </view>
        </view>
    </view>
</view>