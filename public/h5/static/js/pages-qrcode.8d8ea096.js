(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-qrcode"],{"140f":function(t,e,a){"use strict";a.r(e);var i=a("fc96"),o=a("6e4e");for(var n in o)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(n);a("7bcb");var s=a("f0c5"),d=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"6c20a257",null,!1,i["a"],void 0);e["default"]=d.exports},"6e4e":function(t,e,a){"use strict";a.r(e);var i=a("7fcd"),o=a.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);e["default"]=o.a},"7bcb":function(t,e,a){"use strict";var i=a("f531"),o=a.n(i);o.a},"7fcd":function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={data:function(){return{headHeight:100,windowHeight:0,headTop:0,showCode:!0}},onShow:function(){var t=this,e=this.$wanlshop.wanlsys();this.headTop=e.top,this.windowHeight=e.windowHeight,setTimeout((function(){uni.setNavigationBarColor({frontColor:t.$store.state.common.modulesData.homeModules.page?t.$store.state.common.modulesData.homeModules.page.style.navigationBarTextStyle:"",backgroundColor:t.$store.state.common.modulesData.homeModules.page?t.$store.state.common.modulesData.homeModules.page.style.navigationBarBackgroundColor:""})}),200)},methods:{onBack:function(){console.log("onBackonBack"),this.$wanlshop.back(1)},showPayment:function(){this.showCode=!1},scanCode:function(){this.showCode=!0,this.$wanlshop.msg("暂不支持H5扫码")}}};e.default=i},c0d3:function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/* 定义主题颜色变量 */\n/* 渐变按钮 */\n/* 页面左右间距 */\n/* 文字尺寸 */\n/*文字颜色*/\n/* 边框颜色 */\n/* 图片加载中颜色 */\n/* 行为相关颜色 */\n/* 以下Token页面 */.qrCode[data-v-6c20a257]{min-height:100vh;background-color:#081a1e;padding-top:%?350?%;position:relative}.qrCode .bg[data-v-6c20a257]{width:100vw;height:100vh;position:absolute;top:0;left:0;z-index:1}.qrCode .close[data-v-6c20a257]{width:%?50?%;height:%?50?%;position:absolute;left:%?24?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);z-index:999999}.qrCode__head[data-v-6c20a257]{position:absolute;top:0;left:0;right:0;z-index:999;background-size:100% auto;background-repeat:no-repeat}.qrCode__head .top-bar[data-v-6c20a257]{position:absolute;top:0;left:0;right:0;z-index:99999}.qrCode__head .navigater[data-v-6c20a257]{height:%?86?%;position:relative;padding-left:%?25?%;padding-right:%?25?%}.qrCode__head .navigater .logo[data-v-6c20a257]{width:%?50?%;height:%?50?%;position:absolute;left:%?24?%;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);z-index:999999}.qrCode__head .navigater .logoText[data-v-6c20a257]{margin:0 auto;display:inline-block;width:%?185?%;height:%?29?%;position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);z-index:999999}.qrCode .main[data-v-6c20a257]{margin-bottom:%?200?%;position:relative;z-index:999}.qrCode .main .code-main[data-v-6c20a257]{width:%?450?%;height:%?450?%}.qrCode .main .payment[data-v-6c20a257]{width:%?480?%;height:%?610?%}.qrCode .footer[data-v-6c20a257]{box-sizing:border-box;padding:0 %?100?%;position:relative;z-index:999;width:100%}.qrCode .footer .icon[data-v-6c20a257]{width:%?100?%;height:%?100?%}',""]),t.exports=e},f531:function(t,e,a){var i=a("c0d3");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=a("4f06").default;o("2825fe8e",i,!0,{sourceMap:!1,shadowMode:!1})},fc96:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"qrCode"},[a("v-uni-image",{staticClass:"bg",attrs:{src:t.$wanlshop.imgstc("/default/code-bg.png"),mode:""}}),a("v-uni-view",{staticClass:"qrCode__head",style:{height:t.headHeight+"rpx"}},[a("v-uni-view",{staticClass:"top-bar",style:{paddingTop:t.headTop+"px"}},[a("v-uni-view",{staticClass:"navigater"},[a("v-uni-image",{staticClass:"close",attrs:{src:t.$wanlshop.imgstc("/dragon/close.png"),mode:""},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onBack.apply(void 0,arguments)}}}),a("v-uni-image",{staticClass:"logoText",attrs:{src:t.$wanlshop.imgstc("/home/<USER>"),mode:""}})],1)],1)],1),a("v-uni-view",{staticClass:"main flex align-center justify-center"},[t.showCode?a("v-uni-image",{staticClass:"code-main",attrs:{src:t.$wanlshop.imgstc("/default/code-main.png"),mode:""}}):a("v-uni-image",{staticClass:"payment",attrs:{src:t.$wanlshop.imgstc("/default/payment.png"),mode:""}})],1),a("v-uni-view",{staticClass:"footer flex align-center justify-between"},[a("v-uni-view",{staticClass:"flex flex-direction justify-center align-center"},[a("v-uni-image",{staticClass:"icon",attrs:{src:t.$wanlshop.imgstc("/default/qrcode2.png"),mode:""},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.scanCode.apply(void 0,arguments)}}}),a("v-uni-view",{staticClass:"text-df text-white margin-top-sm"},[t._v("二维码")])],1),a("v-uni-view",{staticClass:"flex flex-direction justify-between align-center"},[a("v-uni-image",{staticClass:"icon",attrs:{src:t.$wanlshop.imgstc("/default/qrcode1.png"),mode:""}}),a("v-uni-view",{staticClass:"text-df text-white margin-top-sm"},[t._v("拍照识别")])],1),a("v-uni-view",{staticClass:"flex flex-direction justify-center align-center"},[a("v-uni-image",{staticClass:"icon",attrs:{src:t.$wanlshop.imgstc("/default/qrcode3.png"),mode:""}}),a("v-uni-view",{staticClass:"text-df text-white margin-top-sm"},[t._v("付款码")])],1)],1)],1)},o=[]}}]);