(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-page-topUpGasCard-add"],{"2bcb":function(e,r,t){"use strict";var a=t("65b2"),n=t.n(a);n.a},4844:function(e,r,t){"use strict";t("6a54");var a=t("f5bd").default;Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=a(t("b336")),i={name:"add-card",data:function(){return{formData:{mobile:"",cardNumber:"",code:"",referrer:""},explainTitle:"绑定说明：",explainList:[{title:"您所填的手机导码必须与加油卡开卢时预留的手机号码一致"},{title:"所添加的加油卡为有效且正常使用的主卡"},{title:"目前短信功能只支持广东地区的手机号码验证"},{title:"如需办理加油卡用户，请持有身份证至中石化任意"}],countdown:60,cTimer:null,showClearMobile:!1,showClearCard:!1,showClearCode:!1,showClearReferrer:!1}},methods:{submitForm:function(){var e=this.formData,r=n.default.check(e,[{name:"mobile",checkType:"phoneno",errorMsg:"请输入正确的手机号"},{name:"cardNumber",checkType:"notnull",errorMsg:"请输入加油卡卡号"},{name:"code",checkType:"notnull",errorMsg:"请输入验证码"}]);r?this.$wanlshop.back(1):this.$wanlshop.msg(n.default.error)},sendMessage:function(){var e=this,r=this.formData,t=n.default.check(r,[{name:"mobile",checkType:"phoneno",errorMsg:"请输入正确的手机号"}]);t?uni.request({url:"/wanlshop/sms/send",method:"POST",data:{mobile:this.formData.mobile},loadingTip:"验证码发送中...",success:function(r){e.startTimer(),e.$wanlshop.msg("验证码发送成功")}}):this.$wanlshop.msg(n.default.error)},startTimer:function(){var e=this;null==this.cTimer&&(this.cTimer=setInterval((function(){e.countdown--,0==e.countdown&&e.clearTimer()}),1e3))},clearTimer:function(){clearInterval(this.cTimer),this.cTimer=null,this.countdown=60},inputMobile:function(e,r){var t=e.detail.value.length;t>0&&(this.showClearMobile=!0)},focusMobile:function(e){var r=e.detail.value.length;r>0&&(this.showClearMobile=!0)},blurMobile:function(){this.showClearMobile=!1},clearMobile:function(){this.formData.mobile=""},inputCard:function(e){var r=e.detail.value.length;r>0&&(this.showClearCard=!0)},focusCard:function(e){var r=e.detail.value.length;r>0&&(this.showClearCard=!0)},blurCard:function(){this.showClearCard=!1},clearCard:function(){this.formData.cardNumber=""},inputCode:function(e){var r=e.detail.value.length;r>0&&(this.showClearCode=!0)},focusCode:function(e){var r=e.detail.value.length;r>0&&(this.showClearCode=!0)},blurCode:function(){this.showClearCode=!1},clearCode:function(){this.formData.code=""},inputReferrer:function(e){var r=e.detail.value.length;r>0&&(this.showClearReferrer=!0)},focusReferrer:function(e){var r=e.detail.value.length;r>0&&(this.showClearReferrer=!0)},blurReferrer:function(){this.showClearReferrer=!1},clearReferrer:function(){this.formData.referrer=""}}};r.default=i},"65b2":function(e,r,t){var a=t("ce56");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var n=t("967d").default;n("957b8fb2",a,!0,{sourceMap:!1,shadowMode:!1})},"67aa":function(e,r,t){"use strict";t.r(r);var a=t("ef7d"),n=t("ac0e");for(var i in n)["default"].indexOf(i)<0&&function(e){t.d(r,e,(function(){return n[e]}))}(i);t("2bcb");var o=t("828b"),s=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"4c5b53be",null,!1,a["a"],void 0);r["default"]=s.exports},ac0e:function(e,r,t){"use strict";t.r(r);var a=t("4844"),n=t.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){t.d(r,e,(function(){return a[e]}))}(i);r["default"]=n.a},b336:function(e,r,t){t("23f4"),t("7d2f"),t("5c47"),t("9c4e"),t("ab80"),t("0506"),t("64aa"),t("5ef2"),e.exports={error:"",check:function(e,r){for(var t=0;t<r.length;t++){if(!r[t].checkType)return!0;if(!r[t].name)return!0;if(!r[t].errorMsg)return!0;if(!e[r[t].name])return this.error=r[t].errorMsg,!1;switch(r[t].checkType){case"string":var a=new RegExp("^.{"+r[t].checkRule+"}$");if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"int":a=new RegExp("^(-[1-9]|[1-9])[0-9]{"+r[t].checkRule+"}$");if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"between":if(!this.isNumber(e[r[t].name]))return this.error=r[t].errorMsg,!1;var n=r[t].checkRule.split(",");if(n[0]=Number(n[0]),n[1]=Number(n[1]),e[r[t].name]>n[1]||e[r[t].name]<n[0])return this.error=r[t].errorMsg,!1;break;case"betweenD":a=/^-?[1-9][0-9]?$/;if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;n=r[t].checkRule.split(",");if(n[0]=Number(n[0]),n[1]=Number(n[1]),e[r[t].name]>n[1]||e[r[t].name]<n[0])return this.error=r[t].errorMsg,!1;break;case"betweenF":a=/^-?[0-9][0-9]?.+[0-9]+$/;if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;n=r[t].checkRule.split(",");if(n[0]=Number(n[0]),n[1]=Number(n[1]),e[r[t].name]>n[1]||e[r[t].name]<n[0])return this.error=r[t].errorMsg,!1;break;case"same":if(e[r[t].name]!=r[t].checkRule)return this.error=r[t].errorMsg,!1;break;case"notsame":if(e[r[t].name]==r[t].checkRule)return this.error=r[t].errorMsg,!1;break;case"email":a=/^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"phoneno":a=/^1[0-9]{10,10}$/;if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"zipcode":a=/^[0-9]{6}$/;if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"reg":a=new RegExp(r[t].checkRule);if(!a.test(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"in":if(-1==r[t].checkRule.indexOf(e[r[t].name]))return this.error=r[t].errorMsg,!1;break;case"notnull":if(null==e[r[t].name]||e[r[t].name].length<1)return this.error=r[t].errorMsg,!1;break;case"length6":if(null==e[r[t].name]||e[r[t].name].length<6)return this.error=r[t].errorMsg,!1;break}}return!0},isNumber:function(e){return/^-?[1-9][0-9]?.?[0-9]*$/.test(e)}}},ce56:function(e,r,t){var a=t("c86c");r=a(!1),r.push([e.i,'@charset "UTF-8";\n/* 页面左右间距 */\n/* 文字尺寸 */\n/*文字颜色*/\n/* 边框颜色 */\n/* 图片加载中颜色 */\n/* 行为相关颜色 */.add-card__info[data-v-4c5b53be]{margin:%?20?%;background-color:#fff;border-radius:%?10?%;padding:%?10?% %?30?% %?30?% %?30?%}.add-card__info .form-item[data-v-4c5b53be]{display:flex;align-items:center;justify-content:space-between;margin-top:%?20?%;border-bottom:%?1?% solid #d9d9d9;padding:%?20?%;width:100%}.add-card__info .form-item .input[data-v-4c5b53be]{width:90%}.add-card__info .form-item .get-code[data-v-4c5b53be]{display:flex;align-items:center;color:#fe7711}.add-card__info .form-item .get-code .parting-line[data-v-4c5b53be]{height:%?28?%;width:%?1?%;margin:0 %?20?%;background-color:#d9d9d9}.add-card__info .btn[data-v-4c5b53be]{margin:%?60?% 0 0 0;width:100%;padding:%?5?%;border-radius:%?1000?%;background-image:linear-gradient(0deg,#fe7711,#ff9e53 97%);color:#fff;font-size:%?28?%;display:flex;align-items:center;justify-content:center}.add-card__explain[data-v-4c5b53be]{margin:%?20?%;background-color:#fff;border-radius:%?10?%;padding:%?30?%;color:#333;line-height:%?46?%}.add-card__explain .content[data-v-4c5b53be]{color:#666}',""]),e.exports=r},ef7d:function(e,r,t){"use strict";t.d(r,"b",(function(){return n})),t.d(r,"c",(function(){return i})),t.d(r,"a",(function(){return a}));var a={uniIcons:t("ea82").default},n=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("v-uni-view",{staticClass:"add-card"},[t("v-uni-view",{staticClass:"add-card__info"},[t("uni-forms",{staticClass:"form",attrs:{modelValue:e.formData}},[t("v-uni-view",{staticClass:"form-item"},[t("v-uni-input",{attrs:{type:"number",name:"mobile",maxlength:"11",placeholder:"请输入手机号"},on:{input:function(r){arguments[0]=r=e.$handleEvent(r),e.inputMobile.apply(void 0,arguments)},focus:function(r){arguments[0]=r=e.$handleEvent(r),e.focusMobile.apply(void 0,arguments)},blur:function(r){arguments[0]=r=e.$handleEvent(r),e.blurMobile.apply(void 0,arguments)}},model:{value:e.formData.mobile,callback:function(r){e.$set(e.formData,"mobile",r)},expression:"formData.mobile"}}),t("uni-icons",{directives:[{name:"show",rawName:"v-show",value:e.showClearMobile,expression:"showClearMobile"}],attrs:{type:"close",size:"20",color:"#ccc"},on:{click:function(r){arguments[0]=r=e.$handleEvent(r),e.clearMobile.apply(void 0,arguments)}}})],1),t("v-uni-view",{staticClass:"form-item"},[t("v-uni-input",{staticClass:"input",attrs:{type:"number",name:"cardNumber",placeholder:"请输入加油卡卡号"},on:{input:function(r){arguments[0]=r=e.$handleEvent(r),e.inputCard.apply(void 0,arguments)},focus:function(r){arguments[0]=r=e.$handleEvent(r),e.focusCard.apply(void 0,arguments)},blur:function(r){arguments[0]=r=e.$handleEvent(r),e.blurCard.apply(void 0,arguments)}},model:{value:e.formData.cardNumber,callback:function(r){e.$set(e.formData,"cardNumber",r)},expression:"formData.cardNumber"}}),t("uni-icons",{directives:[{name:"show",rawName:"v-show",value:e.showClearCard,expression:"showClearCard"}],attrs:{type:"close",size:"20",color:"#ccc"},on:{click:function(r){arguments[0]=r=e.$handleEvent(r),e.clearCard.apply(void 0,arguments)}}})],1),t("v-uni-view",{staticClass:"form-item flex justify-between"},[t("v-uni-input",{attrs:{type:"number",name:"code",placeholder:"请输入短信验证码"},on:{input:function(r){arguments[0]=r=e.$handleEvent(r),e.inputCode.apply(void 0,arguments)},focus:function(r){arguments[0]=r=e.$handleEvent(r),e.focusCode.apply(void 0,arguments)},blur:function(r){arguments[0]=r=e.$handleEvent(r),e.blurCode.apply(void 0,arguments)}},model:{value:e.formData.code,callback:function(r){e.$set(e.formData,"code",r)},expression:"formData.code"}}),t("v-uni-view",{staticClass:"get-code"},[t("uni-icons",{directives:[{name:"show",rawName:"v-show",value:e.showClearCode,expression:"showClearCode"}],attrs:{type:"close",size:"20",color:"#ccc"},on:{click:function(r){arguments[0]=r=e.$handleEvent(r),e.clearCode.apply(void 0,arguments)}}}),t("v-uni-view",{staticClass:"parting-line"}),0==e.countdown||60==e.countdown?t("v-uni-text",{on:{click:function(r){arguments[0]=r=e.$handleEvent(r),e.sendMessage.apply(void 0,arguments)}}},[e._v("获取验证码")]):t("v-uni-text",[e._v(e._s(e.countdown)+"s后可重新发送")])],1)],1),t("v-uni-view",{staticClass:"form-item"},[t("v-uni-input",{staticClass:"input",attrs:{type:"number",name:"referrer",placeholder:"请输入员工推荐码(选填)"},on:{input:function(r){arguments[0]=r=e.$handleEvent(r),e.inputReferrer.apply(void 0,arguments)},focus:function(r){arguments[0]=r=e.$handleEvent(r),e.focusReferrer.apply(void 0,arguments)},blur:function(r){arguments[0]=r=e.$handleEvent(r),e.blurReferrer.apply(void 0,arguments)}},model:{value:e.formData.referrer,callback:function(r){e.$set(e.formData,"referrer",r)},expression:"formData.referrer"}}),t("uni-icons",{directives:[{name:"show",rawName:"v-show",value:e.showClearReferrer,expression:"showClearReferrer"}],attrs:{type:"close",size:"20",color:"#ccc"},on:{click:function(r){arguments[0]=r=e.$handleEvent(r),e.clearReferrer.apply(void 0,arguments)}}})],1)],1),t("v-uni-button",{staticClass:"btn",on:{click:function(r){arguments[0]=r=e.$handleEvent(r),e.submitForm.apply(void 0,arguments)}}},[e._v("马上绑定")])],1),t("v-uni-view",{staticClass:"add-card__explain"},[t("v-uni-text",[e._v(e._s(e.explainTitle))]),t("v-uni-view",{staticClass:"content"},e._l(e.explainList,(function(r,a){return t("v-uni-view",{key:a},[e._v(e._s(a+1)+"."+e._s(r.title))])})),1)],1)],1)},i=[]}}]);