/* @font-face {
  font-family: 'iconfont';
  src: url('//at.alicdn.com/t/font_3291408_b9evfdv62yw.woff2?t=1648659143626') format('woff2'),
       url('//at.alicdn.com/t/font_3291408_b9evfdv62yw.woff?t=1648659143626') format('woff'),
       url('//at.alicdn.com/t/font_3291408_b9evfdv62yw.ttf?t=1648659143626') format('truetype');
} */
@font-face {
  font-family: 'iconfont';
  src: url('./static/styleyy/iconfont.woff2?t=1658851612477') format('woff2'),
       url('./static/styleyy/iconfont.woff?t=1658851612477') format('woff'),
       url('./static/styleyy/iconfont.ttf?t=1658851612477') format('truetype');
}
.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-CNY:before {
  content: "\e61b";
}
.icon-rencan:before {
  content: "\e8f9";
}
.icon-SDR:before {
  content: "\e627";
}
.wlicon-zhang:before {
  content: "\e658";
}
.wlicon-die:before {
  content: "\e657";
}
/* .wlIcon-balancebd-pay:before {
	content: "\e61b";
} */
/* .wlIcon-balancexnb-pay:before {
	content: "\e61b";
}
.wlIcon-balanceusdt-pay:before {
	content: "\e8f9";
} */