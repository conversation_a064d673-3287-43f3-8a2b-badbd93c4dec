(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-user-setting-security"],{"3b0c":function(e,t,n){"use strict";n.r(t);var s=n("cb23"),i=n.n(s);for(var a in s)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return s[e]}))}(a);t["default"]=i.a},"487b":function(e,t,n){"use strict";n.d(t,"b",(function(){return s})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){}));var s=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",[n("v-uni-view",{staticClass:"edgeInsetTop"}),n("v-uni-view",{staticClass:"cu-list menu sm-border"},[n("v-uni-view",{staticClass:"cu-item arrow",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$wanlshop.auth("/pages/user/auth/resetphone?username="+this.user.username+"&mobile="+(e.user.mobile?e.user.mobile:""))}}},[n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[e._v(e._s(e.user.mobile?"修改":"绑定")+"手机号码")])],1)],1),n("v-uni-view",{staticClass:"cu-item arrow",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toPage("resetpwd")}}},[n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[e._v("修改登录密码")])],1)],1),n("v-uni-view",{staticClass:"cu-item arrow",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toPage("resetpaypwd")}}},[n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[e._v("修改支付密码")])],1)],1)],1),n("v-uni-view",{staticClass:"cu-list menu sm-border",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.noUser.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"cu-item arrow"},[n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[e._v("注销账户")])],1)],1)],1)],1)},i=[]},"6d03":function(e,t,n){"use strict";n.r(t);var s=n("487b"),i=n("3b0c");for(var a in i)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(a);var u=n("828b"),o=Object(u["a"])(i["default"],s["b"],s["c"],!1,null,"6d2b892c",null,!1,s["a"],void 0);t["default"]=o.exports},cb23:function(e,t,n){"use strict";n("6a54");var s=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("c223");var i=s(n("9b1b")),a=n("8f59"),u={data:function(){return{}},computed:(0,i.default)({},(0,a.mapState)(["user"])),methods:{toPage:function(e){this.user.mobile?"resetphone"==e?this.$wanlshop.auth("/pages/user/auth/resetphone?event=resetphone&username=".concat(this.user.username,"&mobile=").concat(this.user.mobile)):"resetpwd"==e?this.$wanlshop.auth("/pages/user/auth/validcode?event=resetpwd&mobile=".concat(this.user.mobile)):"resetpaypwd"==e&&this.$wanlshop.auth("/pages/user/auth/validcode?event=resetpaypwd&username=".concat(this.user.username,"&mobile=").concat(this.user.mobile?this.user.mobile:this.user.email)):this.$wanlshop.msg("请先绑定手机号码")},noUser:function(){this.$wanlshop.msg("功能暂未开放")}}};t.default=u}}]);