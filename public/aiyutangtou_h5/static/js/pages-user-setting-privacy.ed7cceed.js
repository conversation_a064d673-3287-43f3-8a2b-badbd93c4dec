(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-user-setting-privacy"],{"47d7":function(n,t,e){"use strict";e.r(t);var u=e("4d15"),i=e.n(u);for(var r in u)["default"].indexOf(r)<0&&function(n){e.d(t,n,(function(){return u[n]}))}(r);t["default"]=i.a},"4d15":function(n,t,e){"use strict";e("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={data:function(){return{}},methods:{}}},"5f19":function(n,t,e){"use strict";e.d(t,"b",(function(){return u})),e.d(t,"c",(function(){return i})),e.d(t,"a",(function(){}));var u=function(){var n=this.$createElement,t=this._self._c||n;return t("v-uni-view",[t("v-uni-view",{staticClass:"edgeInsetTop"})],1)},i=[]},"81d1":function(n,t,e){"use strict";e.r(t);var u=e("5f19"),i=e("47d7");for(var r in i)["default"].indexOf(r)<0&&function(n){e.d(t,n,(function(){return i[n]}))}(r);var a=e("f0c5"),f=Object(a["a"])(i["default"],u["b"],u["c"],!1,null,"64e3e878",null,!1,u["a"],void 0);t["default"]=f.exports}}]);