@font-face {
	font-family: 'iconfont';
	/* project id 2381716 */
	src: url('https://at.alicdn.com/t/font_2381716_g00fc3mij5.eot');
	src: url('https://at.alicdn.com/t/font_2381716_g00fc3mij5.eot?#iefix') format('embedded-opentype'),
		url('https://at.alicdn.com/t/font_2381716_g00fc3mij5.woff2') format('woff2'),
		url('https://at.alicdn.com/t/font_2381716_g00fc3mij5.woff') format('woff'),
		url('https://at.alicdn.com/t/font_2381716_g00fc3mij5.ttf') format('truetype'),
		url('https://at.alicdn.com/t/font_2381716_g00fc3mij5.svg#iconfont') format('svg');
		
}

.iconfont {
	font-family: "iconfont" !important;
	font-size: 16px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.icon-dagou:before {
	content: "\e780";
}

.icon-h2o:before {
	content: "\e61d";
}

.icon-defc-copy:before {
	content: "\e604";
}

.icon-biaotilogo:before {
	content: "\e60d";
}

.icon-logo:before {
	content: "\e631";
}

.icon-USDT:before {
	content: "\e62c";
}

.icon-xy-teaching-verifies:before {
	content: "\e633";
}

.icon-shandian:before {
	content: "\e757";
}

.icon-shoukuan1:before {
	content: "\e60b";
}

.icon-zhuanzhang1:before {
	content: "\e77d";
}

.icon-back:before {
	content: "\e610";
}

.icon-jifen:before {
	content: "\e621";
}

.icon-jifen1:before {
	content: "\e6ca";
}

.icon-tuandui:before {
	content: "\ee95";
}

.icon-renminbi:before {
	content: "\e600";
}