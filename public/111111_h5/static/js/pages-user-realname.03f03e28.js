(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-user-realname"],{"4b86":function(t,e,a){"use strict";var o=a("df9b"),i=a.n(o);i.a},"7d24":function(t,e,a){"use strict";a.r(e);var o=a("90ca"),i=a("b60e");for(var n in i)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(n);a("4b86");var s=a("f0c5"),c=Object(s["a"])(i["default"],o["b"],o["c"],!1,null,"7e49d22a",null,!1,o["a"],void 0);e["default"]=c.exports},"8fab":function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o={data:function(){return{cameraContext:{},windowHeight:"",wanlsys:{},photoType:"",devicePosition:"back",flashStyle:"off",cameraOnImg:this.$wanlshop.imgstc("/face/camera_on_light.png"),cameraOffImg:this.$wanlshop.imgstc("/face/camera_off_light.png"),flashImgUrl:""}},onLoad:function(t){this.flashImgUrl=this.cameraOnImg;var e=this.$wanlshop.wanlsys();this.wanlsys=e,this.windowHeight=e.windowHeight+e.windowBottom,uni.createCameraContext?this.cameraContext=uni.createCameraContext():uni.showModal({title:"提示",content:"当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。"}),this.photoType=t.photoType,this.devicePosition="back"},methods:{errorCamera:function(t){uni.getSetting({success:function(t){t.authSetting["scope.camera"]||uni.showModal({title:"提示",content:"请开启摄像头权限，否则无法拍照",confirmText:"去开启",success:function(t){t.confirm?uni.openSetting({success:function(t){t.authSetting["scope.camera"],uni.navigateBack({delta:1})}}):t.cancel&&uni.navigateBack({delta:1})}})}})},takePhoto:function(){this.takePhotoForH5()},takePhotoForH5:function(){this.chooseImageByType("camera")},takePhotoForMp:function(){var t=this;uni.showLoading({title:"拍摄中"}),this.cameraContext.takePhoto({quality:"normal",success:function(e){console.log("拍摄照片",e);var a=wx.getFileSystemManager().readFileSync(e.tempImagePath,"base64");t.toRealAuthFun(a),uni.showToast({title:"拍照成功",icon:"none",duration:1200})},fail:function(t){uni.showToast({title:"拍照失败，请检查系统是否授权",icon:"none",duration:1200})}})},chooseImage:function(){this.chooseImageByType("album")},chooseImageByType:function(t){var e=this;uni.chooseImage({count:1,sizeType:["original","compressed"],sourceType:[t],success:function(t){var a=t.tempFilePaths[0],o=e;e.getImgToBase64(a,(function(t){o.toRealAuthFun(t)}))},fail:function(t){}})},getImgToBase64:function(t,e){var a=document.createElement("canvas"),o=a.getContext("2d"),i=new Image;i.crossOrigin="Anonymous",i.onload=function(){a.height=i.height,a.width=i.width,o.drawImage(i,0,0);var t=a.toDataURL("image/png");e(t),a=null},i.src=t},toRealAuthFun:function(t){this.$api.post({url:"/wanlshop/faceid",data:{data:{ImageBase64:t}},toastError:!1,success:function(t){console.log(t),t.data.Sim>70?(uni.showToast({title:"恭喜您！实名认证已通过!",icon:"none",duration:1200}),setTimeout((function(){uni.redirectTo({url:"/pages/user/faceresult"})}),1200)):(uni.showToast({title:"实名认证未通过，请得新认证!",icon:"none",duration:1200}),setTimeout((function(){uni.redirectTo({url:"/pages/user"})}),1200))},fail:function(t){console.log(t),uni.showToast({title:"实名认证未通过，请得新认证",icon:"none",duration:1200}),setTimeout((function(){uni.redirectTo({url:"/pages/user"})}),1e3)}})},openFlash:function(){"on"===this.flashStatus?(this.flashStatus="off",this.flashImgUrl=this.cameraOnImg):(this.flashStatus="on",this.flashImgUrl=this.cameraOffImg)},backCancel:function(){this.$wanlshop.back(1)}}};e.default=o},"90ca":function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){}));var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{style:{height:t.windowHeight+"px",backgroundImage:"url("+t.$wanlshop.imgstc("/face/camera_bg.png")+")",backgroundSize:"100% auto"}},[a("v-uni-camera",{style:{height:t.windowHeight+"px"},attrs:{mode:"normal","device-position":t.devicePosition,flash:t.flashStyle},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.errorCamera.apply(void 0,arguments)}}},[a("v-uni-cover-view",{staticClass:"controls",staticStyle:{width:"100%",height:"100%"}},[a("v-uni-cover-view",{staticClass:"controls1-bgcolor flex flex-direction align-center position-relative"},[a("v-uni-cover-image",{staticClass:"title-image",style:{top:t.wanlsys.top+15+"px"},attrs:{src:t.$wanlshop.imgstc("/face/camera_title_wm.png")}}),a("v-uni-cover-image",{staticClass:"back-btn",style:{top:t.wanlsys.top+20+"px"},attrs:{src:t.$wanlshop.imgstc("/dragon/close.png"),mode:""},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.backCancel.apply(void 0,arguments)}}}),a("v-uni-cover-view",{staticClass:"title-one"},[t._v("请将身份证人像面对准此区")])],1),a("v-uni-cover-view",{staticClass:"controls2-bgcolor flex-direction-row"},[a("v-uni-cover-view",{staticClass:"cover-bg"}),a("v-uni-cover-image",{staticClass:"w569-h828",attrs:{src:t.$wanlshop.imgstc("/face/card_slid.png")}}),a("v-uni-cover-view",{staticClass:"cover-bg"})],1),a("v-uni-cover-view",{staticClass:"controls3-bgcolor flex flex-direction align-center position-relative"},[a("v-uni-cover-view",{staticClass:"title-two"},[t._v("拍摄要求：清晰完整，避免缺边、模糊、反光")]),a("v-uni-cover-view",{staticClass:"bottom font-36-fff"},[a("v-uni-cover-view",{staticClass:"wrap"},[a("v-uni-cover-view",{staticClass:"back",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.chooseImage.apply(void 0,arguments)}}},[a("v-uni-cover-image",{staticStyle:{width:"100rpx",height:"100rpx"},attrs:{src:t.$wanlshop.imgstc("/face/face-album.png"),mode:""}})],1),a("v-uni-cover-image",{staticClass:"w131-h131",attrs:{src:t.$wanlshop.imgstc("/face/camera_take_btn.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.takePhoto.apply(void 0,arguments)}}}),a("v-uni-cover-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openFlash.apply(void 0,arguments)}}},[a("v-uni-cover-image",{staticStyle:{width:"100rpx",height:"100rpx"},attrs:{src:t.flashImgUrl,mode:""}})],1)],1)],1)],1)],1)],1)],1)},i=[]},"95c6":function(t,e,a){var o=a("24fb");e=o(!1),e.push([t.i,".icon-w569-h828[data-v-7e49d22a]{width:%?569?%;height:%?828?%}.controls[data-v-7e49d22a]{display:flex;align-items:center;justify-content:center;flex-direction:column;position:absolute;top:0}.controls1-bgcolor[data-v-7e49d22a]{list-style:none;padding:0;margin:0;width:100%;height:100%;background-color:rgba(9,8,8,.6)}.controls1-bgcolor .back-btn[data-v-7e49d22a]{width:%?50?%;height:%?50?%;position:absolute;left:%?24?%;-webkit-transform:translateY(-50%);transform:translateY(-50%);z-index:999999}.controls2-bgcolor[data-v-7e49d22a]{list-style:none;padding:0;margin:0;width:100%;height:%?1162?%;display:flex;align-items:center;justify-content:center}.controls2-bgcolor1[data-v-7e49d22a]{width:7%;height:150%;background-color:rgba(9,8,8,.6)}.controls3-bgcolor[data-v-7e49d22a]{list-style:none;padding:0;margin:0;width:100%;height:120%;background-color:rgba(9,8,8,.6)}.bottom[data-v-7e49d22a]{position:absolute;bottom:%?160?%;width:100%}.bottom .wrap[data-v-7e49d22a]{display:flex;align-items:center;justify-content:space-between;height:%?100?%;padding:0 %?123?%}.w569-h828[data-v-7e49d22a]{width:%?610?%;height:100%;background-color:transparent!important}.w131-h131[data-v-7e49d22a]{width:%?100?%;height:%?100?%}.font-36-fff[data-v-7e49d22a]{font-size:%?36?%;color:#fff}.flex-direction-row[data-v-7e49d22a]{flex-direction:row}.cover-bg[data-v-7e49d22a]{width:%?75?%;height:103%;background-color:rgba(9,8,8,.6)}.title-image[data-v-7e49d22a]{width:%?185?%;height:%?29?%;position:absolute}.title-one[data-v-7e49d22a]{color:#fff;font-size:%?36?%;position:absolute;bottom:%?82?%}.title-two[data-v-7e49d22a]{color:#fff;font-size:%?28?%;position:absolute;top:%?54?%}",""]),t.exports=e},b60e:function(t,e,a){"use strict";a.r(e);var o=a("8fab"),i=a.n(o);for(var n in o)["default"].indexOf(n)<0&&function(t){a.d(e,t,(function(){return o[t]}))}(n);e["default"]=i.a},df9b:function(t,e,a){var o=a("95c6");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var i=a("4f06").default;i("53b0383e",o,!0,{sourceMap:!1,shadowMode:!1})}}]);