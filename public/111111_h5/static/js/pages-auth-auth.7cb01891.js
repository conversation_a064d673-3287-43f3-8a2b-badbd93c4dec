(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-auth-auth"],{1617:function(e,t,a){a("4d63"),a("c607"),a("ac1f"),a("2c3e"),a("25f0"),a("00b4"),a("a9e3"),a("c975"),e.exports={error:"",check:function(e,t){for(var a=0;a<t.length;a++){if(!t[a].checkType)return!0;if(!t[a].name)return!0;if(!t[a].errorMsg)return!0;if(!e[t[a].name])return this.error=t[a].errorMsg,!1;switch(t[a].checkType){case"string":var n=new RegExp("^.{"+t[a].checkRule+"}$");if(!n.test(e[t[a].name]))return this.error=t[a].errorMsg,!1;break;case"int":n=new RegExp("^(-[1-9]|[1-9])[0-9]{"+t[a].checkRule+"}$");if(!n.test(e[t[a].name]))return this.error=t[a].errorMsg,!1;break;case"between":if(!this.isNumber(e[t[a].name]))return this.error=t[a].errorMsg,!1;var i=t[a].checkRule.split(",");if(i[0]=Number(i[0]),i[1]=Number(i[1]),e[t[a].name]>i[1]||e[t[a].name]<i[0])return this.error=t[a].errorMsg,!1;break;case"betweenD":n=/^-?[1-9][0-9]?$/;if(!n.test(e[t[a].name]))return this.error=t[a].errorMsg,!1;i=t[a].checkRule.split(",");if(i[0]=Number(i[0]),i[1]=Number(i[1]),e[t[a].name]>i[1]||e[t[a].name]<i[0])return this.error=t[a].errorMsg,!1;break;case"betweenF":n=/^-?[0-9][0-9]?.+[0-9]+$/;if(!n.test(e[t[a].name]))return this.error=t[a].errorMsg,!1;i=t[a].checkRule.split(",");if(i[0]=Number(i[0]),i[1]=Number(i[1]),e[t[a].name]>i[1]||e[t[a].name]<i[0])return this.error=t[a].errorMsg,!1;break;case"same":if(e[t[a].name]!=t[a].checkRule)return this.error=t[a].errorMsg,!1;break;case"notsame":if(e[t[a].name]==t[a].checkRule)return this.error=t[a].errorMsg,!1;break;case"email":n=/^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;if(!n.test(e[t[a].name]))return this.error=t[a].errorMsg,!1;break;case"phoneno":n=/^1[0-9]{10,10}$/;if(!n.test(e[t[a].name]))return this.error=t[a].errorMsg,!1;break;case"zipcode":n=/^[0-9]{6}$/;if(!n.test(e[t[a].name]))return this.error=t[a].errorMsg,!1;break;case"reg":n=new RegExp(t[a].checkRule);if(!n.test(e[t[a].name]))return this.error=t[a].errorMsg,!1;break;case"in":if(-1==t[a].checkRule.indexOf(e[t[a].name]))return this.error=t[a].errorMsg,!1;break;case"notnull":if(null==e[t[a].name]||e[t[a].name].length<1)return this.error=t[a].errorMsg,!1;break;case"length6":if(null==e[t[a].name]||e[t[a].name].length<6)return this.error=t[a].errorMsg,!1;break}}return!0},isNumber:function(e){return/^-?[1-9][0-9]?.?[0-9]*$/.test(e)}}},"39c9":function(e,t,a){"use strict";a.r(t);var n=a("4cc2"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},"3d13":function(e,t,a){"use strict";a.r(t);var n=a("77dc"),i=a("39c9");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("95b4");var o=a("f0c5"),c=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"66e4327c",null,!1,n["a"],void 0);t["default"]=c.exports},"43bc":function(e,t,a){var n=a("67d4");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var i=a("4f06").default;i("0acab12e",n,!0,{sourceMap:!1,shadowMode:!1})},"4cc2":function(e,t,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("1617")),r={data:function(){return{submitDisabled:!0,account:"",pageroute:"",headHeight:0,headTop:0,checked:1,loginRes:{}}},onLoad:function(e){this.account=e.name,this.pageroute=e.url,e.inviter&&(this.inviter=e.inviter)},onShow:function(){var e=this.$wanlshop.wanlsys();console.log("sys===>",e),this.headTop=e.top,this.headHeight=e.height},methods:{decryptPhoneNumber:function(e){var t=this;"getPhoneNumber:fail user deny"!=e.detail.errMsg&&this.$api.post({url:"/wanlshop/user/phone",data:{encryptedData:e.detail.encryptedData,iv:e.detail.iv,code:this.loginRes.code,client_id:uni.getStorageSync("wanlshop:chat_client_id")?uni.getStorageSync("wanlshop:chat_client_id"):null},success:function(e){console.log("res===>",e),t.$store.dispatch("user/login",e),t.$store.dispatch("cart/login"),uni.reLaunch({url:"/pages/user"})},fail:function(e){console.log("err===>",e.data),"请先注册用户"==e.data.msg&&(console.log(3321312),t.register())}})},onCheck:function(){this.checked=1==this.checked?2:1},onKeyInput:function(e){this.submitDisabled=!1},formSubmit:function(e){var t=this;if(2==this.checked){var a=e.detail.value,n=i.default.check(a,[{name:"account",checkType:"notnull",errorMsg:"请输入用户名"},{name:"password",checkType:"string",checkRule:"6,16",errorMsg:"密码至少6位"}]);n?this.$api.post({url:"/wanlshop/user/login",data:{account:a.account,password:a.password,client_id:uni.getStorageSync("wanlshop:chat_client_id")?uni.getStorageSync("wanlshop:chat_client_id"):null},success:function(e){t.$store.dispatch("user/login",e),t.$store.dispatch("cart/login"),t.$store.dispatch("chat/get"),uni.reLaunch({url:"/pages/user"})}}):this.$wanlshop.msg(i.default.error)}else this.checked=1,setTimeout((function(){t.checked=0,t.$wanlshop.msg("请先同意用户协议和隐私保护声明")}),300)},retrieve:function(){this.$wanlshop.to("retrieve?url=".concat(this.pageroute))},phone:function(){this.$wanlshop.to("phone?url=".concat(this.pageroute))},register:function(){this.$wanlshop.to("register?url=".concat(this.pageroute))},help:function(){this.$wanlshop.to("/pages/user/help?url=".concat(this.pageroute))}}};t.default=r},"67d4":function(e,t,a){var n=a("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/* 定义主题颜色变量 */\n/* 渐变按钮 */\n/* 页面左右间距 */\n/* 文字尺寸 */\n/*文字颜色*/\n/* 边框颜色 */\n/* 图片加载中颜色 */\n/* 行为相关颜色 */\n/* 以下Token页面 */.wx-btn[data-v-66e4327c]{background:none;color:#818181;font-size:%?28?%;border:none!important;outline:none;display:flex;flex-direction:column;justify-content:center;align-items:center}.wx-btn uni-text[data-v-66e4327c]{margin-top:%?10?%}.wx-btn[data-v-66e4327c]::after{border:none}.auth[data-v-66e4327c]{min-height:100vh;background-color:#f3f4f4;background-size:100% auto;background-repeat:no-repeat}.auth__head[data-v-66e4327c]{z-index:1;margin-bottom:%?400?%}.auth__head .navigater[data-v-66e4327c]{height:%?86?%;position:relative}.auth__head .navigater .logo[data-v-66e4327c]{width:%?50?%;height:%?50?%}.auth__head .navigater .title[data-v-66e4327c]{position:absolute;left:50%;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);z-index:999999}.auth .form[data-v-66e4327c]{width:100%;padding:0 %?25?%}.auth .auth-group[data-v-66e4327c]{padding:%?1?% %?30?%;display:flex;align-items:center;min-height:%?90?%;justify-content:space-between;margin-bottom:%?25?%}.auth .auth-group uni-input[data-v-66e4327c]{flex:1;font-size:%?33?%;color:#250f00;padding-right:%?20?%}.auth .auth-group .placeholder[data-v-66e4327c]{color:#b3b3b3}.auth .auth-button[data-v-66e4327c]{padding:%?25?% 0 %?50?% 0}.auth .auth-button .cu-btn[data-v-66e4327c]{height:%?90?%}.auth .text-center[data-v-66e4327c]{color:#3f2f21}.auth .agreement[data-v-66e4327c]{font-size:%?24?%;line-height:%?40?%}.auth .agreement uni-text[data-v-66e4327c]{color:#e12430}.auth .checkBox[data-v-66e4327c]{margin-right:%?10?%}.auth .checkBox uni-image[data-v-66e4327c]{width:%?36?%;height:%?36?%}.auth .checkBox .check[data-v-66e4327c]{box-sizing:border-box;width:%?36?%;height:%?36?%;background:#fff;border:%?2?% solid #ccc;border-radius:%?18?%}.auth .auth-clause[data-v-66e4327c]{display:flex;align-items:center;font-size:%?25?%;color:#909090}.auth .shake-horizontal[data-v-66e4327c]{-webkit-animation-name:shake-horizontal;animation-name:shake-horizontal;-webkit-animation-duration:.1s;animation-duration:.1s;-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite;-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out;-webkit-animation-delay:0s;animation-delay:0s;-webkit-animation-play-state:running;animation-play-state:running}.auth .auth-clause uni-checkbox[data-v-66e4327c]{margin-right:%?6?%;-webkit-transform:scale(.7);transform:scale(.7)}.auth .auth-clause uni-text[data-v-66e4327c]{margin:0 %?10?%;color:#ed6d0f}.auth .btns[data-v-66e4327c]{background:linear-gradient(167deg,#eb5d77,#e93323);border-radius:%?35?%;text-align:center;color:#fff;font-size:%?36?%;width:%?560?%;height:%?80?%;line-height:%?80?%;margin-top:%?70?%}.auth .other[data-v-66e4327c]{color:#818181;margin-top:%?30?%;margin-bottom:%?30?%}.auth .other .line[data-v-66e4327c]{display:inline-block;width:%?220?%;height:%?1?%;background:#bbb}.auth .wechat-login .wechat[data-v-66e4327c]{width:%?90?%;height:%?90?%}',""]),e.exports=t},"77dc":function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"auth",style:{backgroundImage:"url("+e.$wanlshop.imgstc("/default/bg_img.png")+")"}},[a("v-uni-view",{staticClass:"auth__head",style:{paddingTop:e.headTop+"px"}},[a("v-uni-view",{staticClass:"navigater flex align-center"},[a("v-uni-view",{staticClass:"action",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$wanlshop.back(1)}}},[a("v-uni-image",{staticClass:"white-back",attrs:{src:e.$wanlshop.imgstc("/default/white_back.png"),mode:""}})],1),a("v-uni-view",{staticClass:"title text-white text-xl"},[e._v("登录/注册")])],1)],1),a("v-uni-view",{staticClass:"form"},[a("v-uni-form",{on:{submit:function(t){arguments[0]=t=e.$handleEvent(t),e.formSubmit.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"auth-group radius-bock bg-white wlian-grey-light"},[a("v-uni-input",{attrs:{type:"text",placeholder:"请输入手机号","confirm-type":"next",maxlength:"16","placeholder-class":"placeholder",name:"account",value:e.account}})],1),a("v-uni-view",{staticClass:"auth-group radius-bock bg-white wlian-grey-light"},[a("v-uni-input",{attrs:{type:"text",placeholder:"请输入登录密码",password:"true","confirm-type":"done",maxlength:"16","placeholder-class":"placeholder",name:"password"},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.onKeyInput.apply(void 0,arguments)}}})],1),a("v-uni-view",{staticClass:"text-right"},[a("v-uni-text",{on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.register.apply(void 0,arguments)}}},[e._v("注册")]),a("v-uni-text",[e._v(e._s(" | "))]),a("v-uni-text",{on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.phone.apply(void 0,arguments)}}},[e._v("手机验证登录")]),a("v-uni-text",[e._v(e._s(" | "))]),a("v-uni-text",{on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.retrieve.apply(void 0,arguments)}}},[e._v("忘记密码")])],1),a("v-uni-view",{staticClass:"flex flex-direction"},[a("v-uni-button",{staticClass:"btns text-center text-white radius-native-40 text-xl",attrs:{"form-type":"submit",disabled:e.submitDisabled}},[e._v("登录")])],1),a("v-uni-view",{staticClass:"agreement padding-lr-sm margin-top flex",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onCheck.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"checkBox margin-right-sm"},[2==e.checked?a("v-uni-image",{attrs:{src:e.$wanlshop.imgstc("/login/check.png"),mode:""}}):a("v-uni-view",{staticClass:"check"})],1),a("v-uni-view",[e._v("我已认真阅读并同意wellmiss商城"),a("v-uni-text",[e._v("《服务协议》、《隐私保护政策》")])],1)],1),a("v-uni-view",{staticClass:"other text-min flex align-center justify-center"},[a("v-uni-text",{staticClass:"line"}),a("v-uni-text",[e._v("其他登录方式")]),a("v-uni-text",{staticClass:"line"})],1)],1)],1)],1)},i=[]},"95b4":function(e,t,a){"use strict";var n=a("43bc"),i=a.n(n);i.a}}]);