(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-user-bank-add"],{"0369":function(a,e,n){"use strict";n.d(e,"b",(function(){return t})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){}));var t=function(){var a=this,e=a.$createElement,n=a._self._c||e;return n("v-uni-view",[n("v-uni-view",{staticClass:"edgeInsetTop"}),n("v-uni-view",{staticClass:"cu-form-group"},[n("v-uni-view",{staticClass:"title"},[a._v("选择银行")]),n("v-uni-picker",{attrs:{value:a.index,range:a.bankList,"range-key":"bankName"},on:{change:function(e){arguments[0]=e=a.$handleEvent(e),a.bankChange.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"picker"},[a.index>-1?n("v-uni-view",{staticClass:"flex justify-end align-center"},[n("v-uni-image",{attrs:{src:"/static/images/bank/"+a.bankList[a.index].bankCode+".png"}}),n("v-uni-view",{staticClass:"margin-left-xs"},[a._v(a._s(a.bankList[a.index].bankName))])],1):n("v-uni-view",[a._v("请选择")])],1)],1)],1),n("v-uni-view",{staticClass:"cu-form-group"},[n("v-uni-view",{staticClass:"title"},[a._v("收款账号")]),a.index==a.bankList.length-2?n("v-uni-input",{attrs:{type:"text",placeholder:"填写支付宝账号"},model:{value:a.bankData.cardCode,callback:function(e){a.$set(a.bankData,"cardCode",e)},expression:"bankData.cardCode"}}):a._e(),a.index==a.bankList.length-1?n("v-uni-input",{attrs:{type:"text",placeholder:"填写微信账号"},model:{value:a.bankData.cardCode,callback:function(e){a.$set(a.bankData,"cardCode",e)},expression:"bankData.cardCode"}}):a._e(),0!=a.index&&1!=a.index?n("v-uni-input",{attrs:{type:"text",placeholder:"填写银行卡账号"},model:{value:a.bankData.cardCode,callback:function(e){a.$set(a.bankData,"cardCode",e)},expression:"bankData.cardCode"}}):a._e()],1),0!=a.index&&1!=a.index?n("v-uni-view",{staticClass:"cu-form-group"},[n("v-uni-view",{staticClass:"title"},[a._v("开户行名称")]),n("v-uni-input",{attrs:{type:"text",placeholder:"填写开户支行名称"},model:{value:a.bankData.bankOpen,callback:function(e){a.$set(a.bankData,"bankOpen",e)},expression:"bankData.bankOpen"}})],1):a._e(),0==a.index||1==a.index?n("v-uni-view",{staticClass:"cu-form-group"},[n("v-uni-view",{staticClass:"title"},[a._v("上传二维码")])],1):a._e(),0==a.index||1==a.index?n("v-uni-view",{staticClass:"grid col-4 grid-square flex-sub padding-lr"},[a.bankData.image?n("v-uni-view",{staticClass:"bg-img",on:{click:function(e){arguments[0]=e=a.$handleEvent(e),a.viewImage(1)}}},[n("v-uni-image",{attrs:{src:a.$wanlshop.oss(a.bankData.image,90,90),mode:"aspectFill"}}),n("v-uni-view",{staticClass:"cu-tag bg-red",on:{click:function(e){e.stopPropagation(),arguments[0]=e=a.$handleEvent(e),a.delImg()}}},[n("v-uni-text",{staticClass:"wlIcon-31guanbi"})],1)],1):a._e(),n("v-uni-view",{staticClass:"dashed",on:{click:function(e){arguments[0]=e=a.$handleEvent(e),a.chooseImage()}}},[n("v-uni-text",{staticClass:"wlIcon-31paishe"})],1)],1):a._e(),n("v-uni-view",{staticClass:"cu-form-group"},[n("v-uni-view",{staticClass:"title"},[a._v("持卡姓名")]),n("v-uni-input",{attrs:{type:"text",maxlength:"40",placeholder:"持账户人姓名",disabled:!!a.truename},model:{value:a.bankData.username,callback:function(e){a.$set(a.bankData,"username",e)},expression:"bankData.username"}})],1),n("v-uni-view",{staticClass:"cu-form-group"},[n("v-uni-view",{staticClass:"title"},[a._v("手机号码")]),n("v-uni-input",{attrs:{type:"number",maxlength:"11",placeholder:"持账户人手机号"},model:{value:a.bankData.mobile,callback:function(e){a.$set(a.bankData,"mobile",e)},expression:"bankData.mobile"}})],1),n("v-uni-view",{staticClass:"padding-bj flex flex-direction margin-top"},[n("v-uni-button",{staticClass:"cu-btn bg-gradual-redpink lg",on:{click:function(e){arguments[0]=e=a.$handleEvent(e),a.confirm.apply(void 0,arguments)}}},[a._v("完成")])],1)],1)},i=[]},2946:function(a,e,n){var t=n("ec83");t.__esModule&&(t=t.default),"string"===typeof t&&(t=[[a.i,t,""]]),t.locals&&(a.exports=t.locals);var i=n("967d").default;i("349bbe6d",t,!0,{sourceMap:!1,shadowMode:!1})},5867:function(a,e,n){"use strict";n.r(e);var t=n("cbc6"),i=n.n(t);for(var s in t)["default"].indexOf(s)<0&&function(a){n.d(e,a,(function(){return t[a]}))}(s);e["default"]=i.a},"6f73":function(a,e,n){"use strict";n.r(e);var t=n("0369"),i=n("5867");for(var s in i)["default"].indexOf(s)<0&&function(a){n.d(e,a,(function(){return i[a]}))}(s);n("c68b");var o=n("828b"),l=Object(o["a"])(i["default"],t["b"],t["c"],!1,null,"904be5a4",null,!1,t["a"],void 0);e["default"]=l.exports},c68b:function(a,e,n){"use strict";var t=n("2946"),i=n.n(t);i.a},cbc6:function(a,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("5c47"),n("0506");var t={data:function(){return{truename:"",bankData:{username:"",mobile:"",bankCode:"",bankName:"",cardCode:"",bankOpen:"",image:null},index:-1,bankList:[{bankCode:"e-CNY",bankName:"数字人民币钱包"},{bankCode:"BOC",bankName:"中国银行"},{bankCode:"ICBC",bankName:"工商银行"},{bankCode:"ABC",bankName:"农业银行"},{bankCode:"PSBC",bankName:"邮储银行"},{bankCode:"CCB",bankName:"建设银行"},{bankCode:"CMB",bankName:"招商银行"},{bankCode:"COMM",bankName:"交通银行"},{bankCode:"SPDB",bankName:"浦发银行"},{bankCode:"GDB",bankName:"广发银行"},{bankCode:"CMBC",bankName:"民生银行"},{bankCode:"PAB",bankName:"平安银行"},{bankCode:"CEB",bankName:"光大银行"},{bankCode:"CIB",bankName:"兴业银行"},{bankCode:"CITIC",bankName:"中信银行"},{bankCode:"ALIPAY",bankName:"支付宝账户"},{bankCode:"WECHAT",bankName:"微信账户"}]}},methods:{confirm:function(){var a=this.bankData;if(a.bankCode)if(a.cardCode)if(a.username){a.mobile&&/^[1][3,4,5,7,8,9][0-9]{9}$/.test(a.mobile)?(this.$wanlshop.prePage().refreshList(a),this.$wanlshop.back(1)):this.$wanlshop.msg("请填写正确手机号")}else this.$wanlshop.msg("请填写真实姓名");else this.$wanlshop.msg("请选择账号");else this.$wanlshop.msg("请选择银行卡")},bankChange:function(a){this.index=a.detail.value,this.bankData.bankCode=this.bankList[a.detail.value].bankCode,this.bankData.bankName=this.bankList[a.detail.value].bankName,this.bankData.bankOpen=this.bankList[a.detail.value].bankOpen},chooseImage:function(){var a=this;uni.chooseImage({count:1,sizeType:["original","compressed"],sourceType:["album","camera"],success:function(e){a.$api.get({url:"/wanlshop/common/uploadData",success:function(n){uni.getImageInfo({src:e.tempFilePaths[0],success:function(e){a.$api.upload({url:n.uploadurl,filePath:e.path,name:"file",formData:"local"==n.storage?null:n.multipart,success:function(e){a.bankData.image=e.url,a.$wanlshop.msg("收款码上传成功")}})}})}})}})},viewImage:function(a){var e=[];e[a]=this.$wanlshop.oss(this.bankData.image),uni.previewImage({urls:e,current:a})},delImg:function(a){this.bankData.image=null}}};e.default=t},ec83:function(a,e,n){var t=n("c86c");e=t(!1),e.push([a.i,".picker .flex uni-image[data-v-904be5a4]{width:%?40?%;height:%?40?%}",""]),a.exports=e}}]);