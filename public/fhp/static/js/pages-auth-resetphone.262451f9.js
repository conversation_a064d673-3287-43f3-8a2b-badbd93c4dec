(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-auth-resetphone"],{"23a6":function(t,a,e){"use strict";e.r(a);var r=e("cdc5"),n=e.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){e.d(a,t,(function(){return r[t]}))}(o);a["default"]=n.a},6044:function(t,a,e){var r=e("c86c");a=r(!1),a.push([t.i,"uni-page-body[data-v-dbfefa4e]{\r\n\t/* background-color: #FFFFFF; */background-color:initial}body.?%PAGE?%[data-v-dbfefa4e]{background-color:initial}.wanl-title[data-v-dbfefa4e]{padding-bottom:%?130?%;padding-top:%?170?%;font-size:%?68?%}.wanl-title .titleInfo[data-v-dbfefa4e]{font-size:%?56?%}.wanl-weixin-btn-info[data-v-dbfefa4e]{color:#b8b8b8!important}.auth[data-v-dbfefa4e]{margin:0 %?60?%}.auth-group[data-v-dbfefa4e]{padding:%?1?% %?30?%;display:flex;align-items:center;min-height:%?90?%;justify-content:space-between;margin-bottom:%?25?%}.auth-group uni-input[data-v-dbfefa4e]{flex:1;font-size:%?33?%;color:#250f00;padding-right:%?20?%}.auth-group .placeholder[data-v-dbfefa4e]{color:#b3b3b3}.auth-button[data-v-dbfefa4e]{padding:%?25?% 0 %?50?% 0}.auth-button .cu-btn[data-v-dbfefa4e]{height:%?90?%}.text-center[data-v-dbfefa4e]{color:#3f2f21}.auth-clause[data-v-dbfefa4e]{display:flex;align-items:center;font-size:%?25?%;color:#909090}.shake-horizontal[data-v-dbfefa4e]{-webkit-animation-name:shake-horizontal-data-v-dbfefa4e;animation-name:shake-horizontal-data-v-dbfefa4e;-webkit-animation-duration:.1s;animation-duration:.1s;-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite;-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out;-webkit-animation-delay:0s;animation-delay:0s;-webkit-animation-play-state:running;animation-play-state:running}.auth-clause uni-checkbox[data-v-dbfefa4e]{margin-right:%?6?%;-webkit-transform:scale(.7);transform:scale(.7)}.auth-clause uni-text[data-v-dbfefa4e]{margin:0 %?10?%;color:#ed6d0f}.auth-foot[data-v-dbfefa4e]{\r\n\t/* position: fixed; */width:100%;bottom:0;z-index:1024;padding:0 %?60?%\r\n\t/* padding-bottom: calc(env(safe-area-inset-bottom) / 2); */}.auth-foot .oauth[data-v-dbfefa4e]{display:flex;flex-wrap:wrap;align-items:center;min-height:%?160?%;justify-content:space-around}.auth-foot .oauth uni-view[data-v-dbfefa4e]{border:%?2?% solid #fcf7e9}.auth-foot .menu[data-v-dbfefa4e]{display:flex;align-items:center;justify-content:center;margin-bottom:%?100?%;color:#3f2f21;line-height:%?28?%;font-size:%?28?%}.auth-foot uni-text[data-v-dbfefa4e]{width:%?180?%;text-align:center}.auth-foot uni-text[data-v-dbfefa4e]:nth-child(2){border-left:%?1?% solid #ececec}\r\n\r\n/* 验证码 */.auth-title[data-v-dbfefa4e]{padding-bottom:%?30?%;padding-top:%?170?%;font-size:%?60?%}.auth-mobile[data-v-dbfefa4e]{color:#9a9a9a;padding-bottom:%?80?%}.auth-mobile uni-text[data-v-dbfefa4e]{margin-left:%?10?%}.codes[data-v-dbfefa4e]{display:flex;justify-content:space-around;flex-direction:row}.codes uni-input[data-v-dbfefa4e]{background:#fff;border-bottom:%?1?% solid #c3c3c3;width:%?90?%;height:%?90?%;text-align:center}.codes .input[data-v-dbfefa4e]{display:flex;justify-content:center;align-items:center;background:#fff;border-bottom:%?1?% solid #c3c3c3;width:%?90?%;height:%?90?%;font-size:%?40?%;font-weight:500;color:#333}.codes .input .shining[data-v-dbfefa4e]{border:%?1?% solid #ed6d0f;height:%?50?%;animation:shining-data-v-dbfefa4e 1s linear infinite;\r\n\t/* 其它浏览器兼容性前缀 */-webkit-animation:shining-data-v-dbfefa4e 1s linear infinite;-moz-animation:shining-data-v-dbfefa4e 1s linear infinite;-ms-animation:shining-data-v-dbfefa4e 1s linear infinite;-o-animation:shining-data-v-dbfefa4e 1s linear infinite}.codes .active[data-v-dbfefa4e]{border-bottom:%?1?% solid #ed6d0f;caret-color:#ed6d0f}.oneline-codes uni-input[data-v-dbfefa4e]{text-align:center}.auth-again[data-v-dbfefa4e]{padding-top:%?50?%}.auth-again uni-text[data-v-dbfefa4e]{color:#ed6d0f;margin-right:%?40?%}.auth-again .time[data-v-dbfefa4e]{color:#9a9a9a}@-webkit-keyframes shining-data-v-dbfefa4e{0%{opacity:1}50%{opacity:1}50.01%{opacity:0}100%{opacity:0}}.wlIcon-QQ[data-v-dbfefa4e]{color:#12b8f6}.wlIcon-WeChat[data-v-dbfefa4e]{color:#02dc6b}.wlIcon-WeiBo[data-v-dbfefa4e]{color:#d32820}.wlIcon-Xiaomi[data-v-dbfefa4e]{color:#ff6b00}@-webkit-keyframes shake-horizontal-data-v-dbfefa4e{0%{-webkit-transform:translate(%?0?%,%?0?%) rotate(0deg);transform:translate(%?0?%,%?0?%) rotate(0deg)}2%{-webkit-transform:translate(%?-4?%,%?0?%) rotate(0deg);transform:translate(%?-4?%,%?0?%) rotate(0deg)}4%{-webkit-transform:translate(%?2?%,%?0?%) rotate(0deg);transform:translate(%?2?%,%?0?%) rotate(0deg)}6%{-webkit-transform:translate(%?-3?%,%?0?%) rotate(0deg);transform:translate(%?-3?%,%?0?%) rotate(0deg)}8%{-webkit-transform:translate(%?9?%,%?0?%) rotate(0deg);transform:translate(%?9?%,%?0?%) rotate(0deg)}10%{-webkit-transform:translate(%?2?%,%?0?%) rotate(0deg);transform:translate(%?2?%,%?0?%) rotate(0deg)}12%{-webkit-transform:translate(%?-6?%,%?0?%) rotate(0deg);transform:translate(%?-6?%,%?0?%) rotate(0deg)}14%{-webkit-transform:translate(%?-7?%,%?0?%) rotate(0deg);transform:translate(%?-7?%,%?0?%) rotate(0deg)}16%{-webkit-transform:translate(%?2?%,%?0?%) rotate(0deg);transform:translate(%?2?%,%?0?%) rotate(0deg)}18%{-webkit-transform:translate(%?7?%,%?0?%) rotate(0deg);transform:translate(%?7?%,%?0?%) rotate(0deg)}20%{-webkit-transform:translate(%?-1?%,%?0?%) rotate(0deg);transform:translate(%?-1?%,%?0?%) rotate(0deg)}22%{-webkit-transform:translate(%?-10?%,%?0?%) rotate(0deg);transform:translate(%?-10?%,%?0?%) rotate(0deg)}24%{-webkit-transform:translate(%?-10?%,%?0?%) rotate(0deg);transform:translate(%?-10?%,%?0?%) rotate(0deg)}26%{-webkit-transform:translate(%?3?%,%?0?%) rotate(0deg);transform:translate(%?3?%,%?0?%) rotate(0deg)}28%{-webkit-transform:translate(%?-5?%,%?0?%) rotate(0deg);transform:translate(%?-5?%,%?0?%) rotate(0deg)}30%{-webkit-transform:translate(%?4?%,%?0?%) rotate(0deg);transform:translate(%?4?%,%?0?%) rotate(0deg)}32%{-webkit-transform:translate(%?-6?%,%?0?%) rotate(0deg);transform:translate(%?-6?%,%?0?%) rotate(0deg)}34%{-webkit-transform:translate(%?-2?%,%?0?%) rotate(0deg);transform:translate(%?-2?%,%?0?%) rotate(0deg)}36%{-webkit-transform:translate(%?-6?%,%?0?%) rotate(0deg);transform:translate(%?-6?%,%?0?%) rotate(0deg)}38%{-webkit-transform:translate(%?-2?%,%?0?%) rotate(0deg);transform:translate(%?-2?%,%?0?%) rotate(0deg)}40%{-webkit-transform:translate(%?4?%,%?0?%) rotate(0deg);transform:translate(%?4?%,%?0?%) rotate(0deg)}42%{-webkit-transform:translate(%?8?%,%?0?%) rotate(0deg);transform:translate(%?8?%,%?0?%) rotate(0deg)}44%{-webkit-transform:translate(%?-3?%,%?0?%) rotate(0deg);transform:translate(%?-3?%,%?0?%) rotate(0deg)}46%{-webkit-transform:translate(%?-10?%,%?0?%) rotate(0deg);transform:translate(%?-10?%,%?0?%) rotate(0deg)}48%{-webkit-transform:translate(%?4?%,%?0?%) rotate(0deg);transform:translate(%?4?%,%?0?%) rotate(0deg)}50%{-webkit-transform:translate(%?-6?%,%?0?%) rotate(0deg);transform:translate(%?-6?%,%?0?%) rotate(0deg)}52%{-webkit-transform:translate(%?6?%,%?0?%) rotate(0deg);transform:translate(%?6?%,%?0?%) rotate(0deg)}54%{-webkit-transform:translate(%?-8?%,%?0?%) rotate(0deg);transform:translate(%?-8?%,%?0?%) rotate(0deg)}56%{-webkit-transform:translate(%?5?%,%?0?%) rotate(0deg);transform:translate(%?5?%,%?0?%) rotate(0deg)}58%{-webkit-transform:translate(%?9?%,%?0?%) rotate(0deg);transform:translate(%?9?%,%?0?%) rotate(0deg)}60%{-webkit-transform:translate(%?7?%,%?0?%) rotate(0deg);transform:translate(%?7?%,%?0?%) rotate(0deg)}62%{-webkit-transform:translate(%?1?%,%?0?%) rotate(0deg);transform:translate(%?1?%,%?0?%) rotate(0deg)}64%{-webkit-transform:translate(%?2?%,%?0?%) rotate(0deg);transform:translate(%?2?%,%?0?%) rotate(0deg)}66%{-webkit-transform:translate(%?-2?%,%?0?%) rotate(0deg);transform:translate(%?-2?%,%?0?%) rotate(0deg)}68%{-webkit-transform:translate(%?-7?%,%?0?%) rotate(0deg);transform:translate(%?-7?%,%?0?%) rotate(0deg)}70%{-webkit-transform:translate(%?-1?%,%?0?%) rotate(0deg);transform:translate(%?-1?%,%?0?%) rotate(0deg)}72%{-webkit-transform:translate(%?-6?%,%?0?%) rotate(0deg);transform:translate(%?-6?%,%?0?%) rotate(0deg)}74%{-webkit-transform:translate(%?5?%,%?0?%) rotate(0deg);transform:translate(%?5?%,%?0?%) rotate(0deg)}76%{-webkit-transform:translate(%?0?%,%?0?%) rotate(0deg);transform:translate(%?0?%,%?0?%) rotate(0deg)}78%{-webkit-transform:translate(%?9?%,%?0?%) rotate(0deg);transform:translate(%?9?%,%?0?%) rotate(0deg)}80%{-webkit-transform:translate(%?-3?%,%?0?%) rotate(0deg);transform:translate(%?-3?%,%?0?%) rotate(0deg)}82%{-webkit-transform:translate(%?8?%,%?0?%) rotate(0deg);transform:translate(%?8?%,%?0?%) rotate(0deg)}84%{-webkit-transform:translate(%?-2?%,%?0?%) rotate(0deg);transform:translate(%?-2?%,%?0?%) rotate(0deg)}86%{-webkit-transform:translate(%?-1?%,%?0?%) rotate(0deg);transform:translate(%?-1?%,%?0?%) rotate(0deg)}88%{-webkit-transform:translate(%?-3?%,%?0?%) rotate(0deg);transform:translate(%?-3?%,%?0?%) rotate(0deg)}90%{-webkit-transform:translate(%?4?%,%?0?%) rotate(0deg);transform:translate(%?4?%,%?0?%) rotate(0deg)}92%{-webkit-transform:translate(%?0?%,%?0?%) rotate(0deg);transform:translate(%?0?%,%?0?%) rotate(0deg)}94%{-webkit-transform:translate(%?4?%,%?0?%) rotate(0deg);transform:translate(%?4?%,%?0?%) rotate(0deg)}96%{-webkit-transform:translate(%?2?%,%?0?%) rotate(0deg);transform:translate(%?2?%,%?0?%) rotate(0deg)}98%{-webkit-transform:translate(%?-2?%,%?0?%) rotate(0deg);transform:translate(%?-2?%,%?0?%) rotate(0deg)}}@keyframes shake-horizontal-data-v-dbfefa4e{0%{-webkit-transform:translate(%?0?%,%?0?%) rotate(0deg);transform:translate(%?0?%,%?0?%) rotate(0deg)}2%{-webkit-transform:translate(%?-4?%,%?0?%) rotate(0deg);transform:translate(%?-4?%,%?0?%) rotate(0deg)}4%{-webkit-transform:translate(%?2?%,%?0?%) rotate(0deg);transform:translate(%?2?%,%?0?%) rotate(0deg)}6%{-webkit-transform:translate(%?-3?%,%?0?%) rotate(0deg);transform:translate(%?-3?%,%?0?%) rotate(0deg)}8%{-webkit-transform:translate(%?9?%,%?0?%) rotate(0deg);transform:translate(%?9?%,%?0?%) rotate(0deg)}10%{-webkit-transform:translate(%?2?%,%?0?%) rotate(0deg);transform:translate(%?2?%,%?0?%) rotate(0deg)}12%{-webkit-transform:translate(%?-6?%,%?0?%) rotate(0deg);transform:translate(%?-6?%,%?0?%) rotate(0deg)}14%{-webkit-transform:translate(%?-7?%,%?0?%) rotate(0deg);transform:translate(%?-7?%,%?0?%) rotate(0deg)}16%{-webkit-transform:translate(%?2?%,%?0?%) rotate(0deg);transform:translate(%?2?%,%?0?%) rotate(0deg)}18%{-webkit-transform:translate(%?7?%,%?0?%) rotate(0deg);transform:translate(%?7?%,%?0?%) rotate(0deg)}20%{-webkit-transform:translate(%?-1?%,%?0?%) rotate(0deg);transform:translate(%?-1?%,%?0?%) rotate(0deg)}22%{-webkit-transform:translate(%?-10?%,%?0?%) rotate(0deg);transform:translate(%?-10?%,%?0?%) rotate(0deg)}24%{-webkit-transform:translate(%?-10?%,%?0?%) rotate(0deg);transform:translate(%?-10?%,%?0?%) rotate(0deg)}26%{-webkit-transform:translate(%?3?%,%?0?%) rotate(0deg);transform:translate(%?3?%,%?0?%) rotate(0deg)}28%{-webkit-transform:translate(%?-5?%,%?0?%) rotate(0deg);transform:translate(%?-5?%,%?0?%) rotate(0deg)}30%{-webkit-transform:translate(%?4?%,%?0?%) rotate(0deg);transform:translate(%?4?%,%?0?%) rotate(0deg)}32%{-webkit-transform:translate(%?-6?%,%?0?%) rotate(0deg);transform:translate(%?-6?%,%?0?%) rotate(0deg)}34%{-webkit-transform:translate(%?-2?%,%?0?%) rotate(0deg);transform:translate(%?-2?%,%?0?%) rotate(0deg)}36%{-webkit-transform:translate(%?-6?%,%?0?%) rotate(0deg);transform:translate(%?-6?%,%?0?%) rotate(0deg)}38%{-webkit-transform:translate(%?-2?%,%?0?%) rotate(0deg);transform:translate(%?-2?%,%?0?%) rotate(0deg)}40%{-webkit-transform:translate(%?4?%,%?0?%) rotate(0deg);transform:translate(%?4?%,%?0?%) rotate(0deg)}42%{-webkit-transform:translate(%?8?%,%?0?%) rotate(0deg);transform:translate(%?8?%,%?0?%) rotate(0deg)}44%{-webkit-transform:translate(%?-3?%,%?0?%) rotate(0deg);transform:translate(%?-3?%,%?0?%) rotate(0deg)}46%{-webkit-transform:translate(%?-10?%,%?0?%) rotate(0deg);transform:translate(%?-10?%,%?0?%) rotate(0deg)}48%{-webkit-transform:translate(%?4?%,%?0?%) rotate(0deg);transform:translate(%?4?%,%?0?%) rotate(0deg)}50%{-webkit-transform:translate(%?-6?%,%?0?%) rotate(0deg);transform:translate(%?-6?%,%?0?%) rotate(0deg)}52%{-webkit-transform:translate(%?6?%,%?0?%) rotate(0deg);transform:translate(%?6?%,%?0?%) rotate(0deg)}54%{-webkit-transform:translate(%?-8?%,%?0?%) rotate(0deg);transform:translate(%?-8?%,%?0?%) rotate(0deg)}56%{-webkit-transform:translate(%?5?%,%?0?%) rotate(0deg);transform:translate(%?5?%,%?0?%) rotate(0deg)}58%{-webkit-transform:translate(%?9?%,%?0?%) rotate(0deg);transform:translate(%?9?%,%?0?%) rotate(0deg)}60%{-webkit-transform:translate(%?7?%,%?0?%) rotate(0deg);transform:translate(%?7?%,%?0?%) rotate(0deg)}62%{-webkit-transform:translate(%?1?%,%?0?%) rotate(0deg);transform:translate(%?1?%,%?0?%) rotate(0deg)}64%{-webkit-transform:translate(%?2?%,%?0?%) rotate(0deg);transform:translate(%?2?%,%?0?%) rotate(0deg)}66%{-webkit-transform:translate(%?-2?%,%?0?%) rotate(0deg);transform:translate(%?-2?%,%?0?%) rotate(0deg)}68%{-webkit-transform:translate(%?-7?%,%?0?%) rotate(0deg);transform:translate(%?-7?%,%?0?%) rotate(0deg)}70%{-webkit-transform:translate(%?-1?%,%?0?%) rotate(0deg);transform:translate(%?-1?%,%?0?%) rotate(0deg)}72%{-webkit-transform:translate(%?-6?%,%?0?%) rotate(0deg);transform:translate(%?-6?%,%?0?%) rotate(0deg)}74%{-webkit-transform:translate(%?5?%,%?0?%) rotate(0deg);transform:translate(%?5?%,%?0?%) rotate(0deg)}76%{-webkit-transform:translate(%?0?%,%?0?%) rotate(0deg);transform:translate(%?0?%,%?0?%) rotate(0deg)}78%{-webkit-transform:translate(%?9?%,%?0?%) rotate(0deg);transform:translate(%?9?%,%?0?%) rotate(0deg)}80%{-webkit-transform:translate(%?-3?%,%?0?%) rotate(0deg);transform:translate(%?-3?%,%?0?%) rotate(0deg)}82%{-webkit-transform:translate(%?8?%,%?0?%) rotate(0deg);transform:translate(%?8?%,%?0?%) rotate(0deg)}84%{-webkit-transform:translate(%?-2?%,%?0?%) rotate(0deg);transform:translate(%?-2?%,%?0?%) rotate(0deg)}86%{-webkit-transform:translate(%?-1?%,%?0?%) rotate(0deg);transform:translate(%?-1?%,%?0?%) rotate(0deg)}88%{-webkit-transform:translate(%?-3?%,%?0?%) rotate(0deg);transform:translate(%?-3?%,%?0?%) rotate(0deg)}90%{-webkit-transform:translate(%?4?%,%?0?%) rotate(0deg);transform:translate(%?4?%,%?0?%) rotate(0deg)}92%{-webkit-transform:translate(%?0?%,%?0?%) rotate(0deg);transform:translate(%?0?%,%?0?%) rotate(0deg)}94%{-webkit-transform:translate(%?4?%,%?0?%) rotate(0deg);transform:translate(%?4?%,%?0?%) rotate(0deg)}96%{-webkit-transform:translate(%?2?%,%?0?%) rotate(0deg);transform:translate(%?2?%,%?0?%) rotate(0deg)}98%{-webkit-transform:translate(%?-2?%,%?0?%) rotate(0deg);transform:translate(%?-2?%,%?0?%) rotate(0deg)}}",""]),t.exports=a},"62d0":function(t,a,e){"use strict";var r=e("9494"),n=e.n(r);n.a},"630c":function(t,a,e){"use strict";e.d(a,"b",(function(){return r})),e.d(a,"c",(function(){return n})),e.d(a,"a",(function(){}));var r=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("v-uni-view",[e("v-uni-view",{staticClass:"auth"},[e("v-uni-view",{staticClass:"wanl-title"},[t._v("修改"+t._s(t.username)+"手机号")]),e("v-uni-form",{on:{submit:function(a){arguments[0]=a=t.$handleEvent(a),t.formSubmit.apply(void 0,arguments)}}},[e("v-uni-view",{staticClass:"auth-group radius-bock bg-gray wlian-grey-light"},[e("v-uni-input",{attrs:{"confirm-type":"done",placeholder:"请填写您的新手机号码",type:"text","placeholder-class":"placeholder",name:"newphone"},on:{input:function(a){arguments[0]=a=t.$handleEvent(a),t.onKeyInput.apply(void 0,arguments)}}}),e("v-uni-input",{staticStyle:{display:"none"},attrs:{name:"username",disabled:!0,value:t.username}}),e("v-uni-input",{staticStyle:{display:"none"},attrs:{name:"mobile",disabled:!0,value:t.mobile}}),e("v-uni-input",{staticStyle:{display:"none"},attrs:{name:"captcha",disabled:!0,value:t.captcha}})],1),e("v-uni-view",{staticClass:"auth-button flex flex-direction"},[e("v-uni-button",{staticClass:"cu-btn bg-orange sl radius-bock",attrs:{formType:"submit",disabled:t.submitDisabled}},[t._v("修改手机号")])],1)],1)],1)],1)},n=[]},9494:function(t,a,e){var r=e("6044");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var n=e("967d").default;n("02d34c9a",r,!0,{sourceMap:!1,shadowMode:!1})},c6a5:function(t,a,e){"use strict";e.r(a);var r=e("630c"),n=e("23a6");for(var o in n)["default"].indexOf(o)<0&&function(t){e.d(a,t,(function(){return n[t]}))}(o);e("62d0");var s=e("828b"),i=Object(s["a"])(n["default"],r["b"],r["c"],!1,null,"dbfefa4e",null,!1,r["a"],void 0);a["default"]=i.exports},cdc5:function(t,a,e){"use strict";e("6a54");var r=e("f5bd").default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var n=r(e("e258")),o={data:function(){return{username:this.$store.state.user.username,mobile:"",captcha:"",submitDisabled:!0}},onLoad:function(t){this.mobile=t.mobile,this.captcha=t.captcha},methods:{onKeyInput:function(t){this.submitDisabled=!1},formSubmit:function(t){var a=t.detail.value,e=n.default.check(a,[{name:"mobile",checkType:"phoneno",errorMsg:"请输入正确手机号"},{name:"captcha",checkType:"zipcode",errorMsg:"验证码至少6位"},{name:"newphone",checkType:"phoneno",errorMsg:"请输入正确手机号"}]);e?this.$wanlshop.to("/pages/auth/validcode?event=changephone&mobile="+a.newphone):this.$wanlshop.msg(n.default.error)}}};a.default=o},e258:function(t,a,e){e("23f4"),e("7d2f"),e("5c47"),e("9c4e"),e("ab80"),e("0506"),e("64aa"),e("5ef2"),t.exports={error:"",check:function(t,a){for(var e=0;e<a.length;e++){if(!a[e].checkType)return!0;if(!a[e].name)return!0;if(!a[e].errorMsg)return!0;if(!t[a[e].name])return this.error=a[e].errorMsg,!1;switch(a[e].checkType){case"string":var r=new RegExp("^.{"+a[e].checkRule+"}$");if(!r.test(t[a[e].name]))return this.error=a[e].errorMsg,!1;break;case"int":r=new RegExp("^(-[1-9]|[1-9])[0-9]{"+a[e].checkRule+"}$");if(!r.test(t[a[e].name]))return this.error=a[e].errorMsg,!1;break;case"between":if(!this.isNumber(t[a[e].name]))return this.error=a[e].errorMsg,!1;var n=a[e].checkRule.split(",");if(n[0]=Number(n[0]),n[1]=Number(n[1]),t[a[e].name]>n[1]||t[a[e].name]<n[0])return this.error=a[e].errorMsg,!1;break;case"betweenD":r=/^-?[1-9][0-9]?$/;if(!r.test(t[a[e].name]))return this.error=a[e].errorMsg,!1;n=a[e].checkRule.split(",");if(n[0]=Number(n[0]),n[1]=Number(n[1]),t[a[e].name]>n[1]||t[a[e].name]<n[0])return this.error=a[e].errorMsg,!1;break;case"betweenF":r=/^-?[0-9][0-9]?.+[0-9]+$/;if(!r.test(t[a[e].name]))return this.error=a[e].errorMsg,!1;n=a[e].checkRule.split(",");if(n[0]=Number(n[0]),n[1]=Number(n[1]),t[a[e].name]>n[1]||t[a[e].name]<n[0])return this.error=a[e].errorMsg,!1;break;case"same":if(t[a[e].name]!=a[e].checkRule)return this.error=a[e].errorMsg,!1;break;case"notsame":if(t[a[e].name]==a[e].checkRule)return this.error=a[e].errorMsg,!1;break;case"email":r=/^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;if(!r.test(t[a[e].name]))return this.error=a[e].errorMsg,!1;break;case"phoneno":r=/^1[0-9]{10,10}$/;if(!r.test(t[a[e].name]))return this.error=a[e].errorMsg,!1;break;case"zipcode":r=/^[0-9]{6}$/;if(!r.test(t[a[e].name]))return this.error=a[e].errorMsg,!1;break;case"reg":r=new RegExp(a[e].checkRule);if(!r.test(t[a[e].name]))return this.error=a[e].errorMsg,!1;break;case"in":if(-1==a[e].checkRule.indexOf(t[a[e].name]))return this.error=a[e].errorMsg,!1;break;case"notnull":if(null==t[a[e].name]||t[a[e].name].length<1)return this.error=a[e].errorMsg,!1;break;case"length6":if(null==t[a[e].name]||t[a[e].name].length<6)return this.error=a[e].errorMsg,!1;break}}return!0},isNumber:function(t){return/^-?[1-9][0-9]?.?[0-9]*$/.test(t)}}}}]);