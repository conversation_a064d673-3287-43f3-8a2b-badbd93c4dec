<?php

namespace app\common\model;

use think\Model;

/**
 * 会员余额日志模型
 */
class CurrencyRmbLog Extends Model
{

    // 表名
    protected $name = 'user_currency_rmb_log';
    // 开启自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = '';
    // 追加属性
    protected $append = [
        'memoen',
        'memocht',
    ];

    public function getMemoenAttr($value, $data)
    {
        $lang=langMemo('en');
        if(array_key_exists($data['memo'],$lang)){
            return $lang[$data['memo']];
        }
        return $data['memo'];
    }

    public function getMemochtAttr($value, $data)
    {
        $lang=langMemo('cht');
        if(array_key_exists($data['memo'],$lang)){
            return $lang[$data['memo']];
        }
        return $data['memo'];
    }
}
