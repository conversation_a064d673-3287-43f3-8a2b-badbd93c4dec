<?php

namespace app\common\behavior;

use addons\wanlshop\library\WeixinSdk\Mp;
use app\api\model\wanlshop\Goods;
use app\api\model\wanlshop\GoodsShare;
use app\api\model\wanlshop\Order;
use app\api\model\wanlshop\OrderGoods;
use app\api\model\wanlshop\Pay;
use app\api\model\wanlshop\Shop;
use app\common\enum\VipEnum;
use app\common\model\CurrencyNsLog;
use app\common\model\CurrencyRmbLog;
use app\common\model\User;
use app\index\model\wanlshop\Ninestar;
use EasyWeChat\Factory;
use fast\Date;
use fast\Http;
use fast\Random;
use MongoDB\Driver\Query;
use think\Cache;
use think\Db;
use think\Exception;
use think\Hook;
use think\Log;

class iz
{
    public function __construct()
    {
        $this->userNumbers = 0;
        $this->userArray = array();
        $this->userNmNumbers = 0;
        $this->userNmArray = array();
    }


    public function run(&$params)
    {

        if($params['action'] == 'nr'){
//            $this->readJsonFile();
//            echo $this->userNumbers."\n";
            $this->findTeam(73954);
            echo $this->userNmNumbers."\n";
            die;
            




            $users = array(
                '13828734133' => array(
                    '1' => [13945073306,13242429199]//,
//                    '2' => [13936019029]
                ),
                '13242429199' => array(
                    '1' => [15076090768],
                    '2' => [18925270025]
                ),
                '15076090768' => array(
                    '1' => [13823541334],
                    '2' => [13948509515]
                ),
                '18925270025' => array(
                    '1' => [18845677274],
                    '2' => [18714560703]
                ),
                '18714560703' => array(
                    '1' => [18645677280],
                    '2' => [13091777262]
                ),
                '18845677274' => array(
                    '1' => [18745600701],
                    '2' => [13946281148]
                ),
                '13946281148' => array(
                    '1' => [18932194567,13766554198],
                    '2' => [13009741888]
                ),
                '13009741888' => array(
                    '1' => [13079699762,18500822447],
                    '2' => [18946230210,15845632100]
                ),
                '18745600701' => array(
                    '1' => [15204901878,13136631943],
                    '2' => [17745776688]
                ),
                '13136631943' => array(
                    '1' => [15845208337,13019782875,13766554839,13199638855,15164674827,18652157863],
                    '2' => [15663150263,18210173135]
                ),
                '18652157863' => array(
                    '1' => [18513191987,15358393413,13304314557],
                    '2' => [13811001579,13596046835,13079691875]
                ),
                '13079691875' => array(
                    '1' => [13948300996,13766988423],
                    '2' => [15663235100]
                ),
                '15663235100' => array(
                    '1' => [13887685518],
                    '2' => [13136618295]
                ),
                '13766988423' => array(
//                    '1' => [13811001579],
                    '2' => [15801080986]
                ),
                '13948509515' => array(
                    '1' => [13504648515],
                    '2' => [13789601001,18504803407,13904704253]
                ),
                '13904704253' => array(
                    '1' => [18301528449],
                    '2' => [13191339944]
                ),
                '18301528449' => array(
                    '1' => [13359995785,13552162137,15389556187],
                    '2' => [15724721890,18972084197]
                ),
                '13504648515' => array(
                    '1' => [13339333047,13329337153,13555196804,13946617691],
                    '2' => [13349336293]
                ),
                '13349336293' => array(
                    '1' => [13766689927,13339340606],
                    '2' => [13009982251,13946638197]
                ),
                '13339340606' => array(
                    '1' => [15146944143,13329345617,13555171618,15304887551,13274695622,13946638197],
                    '2' => [18714693505,18945189911,13339349118,13684699809,15164668615]
                ),
                '13946617691' => array(
                    '1' => [13895889854,18904694058,13846986819,13946607279,18547070855],
                    '2' => [13846986819,15045009719,13115576006,18645286492,15804692849]
                ),
                '13823541334' => array(
                    '1' => [13199319001]//,
//                    '2' => [13528754313]
                ),
                '13199319001' => array(
                    '1' => [13528754313],
                    '2' => [18303950911,18814573515]
                ),
                '18814573515' => array(
                    '1' => [13845728742,15904575849],
                    '2' => [15245796653,13550888219,13845751696]
                ),
                '13845751696' => array(
                    '1' => [13614572055,13845727691,15047018506,13846587533,13845778767,13845729578],
                    '2' => [13845747249,15704575056,13624705377,13947041188,13513354135]
                ),
                '13528754313' => array(
                    '1' => [13199315551],
                    '2' => [13927459443]
                ),
                '13199315551' => array(
                    '1' => [13313197772],
                    '2' => [13199315550,13045261110,18182875556,13845778550]
                ),
                '13845778550' => array(
                    '1' => [13845738926,15609850178,18545561860],
                    '2' => [18630378382,13846583553]
                ),
                '18545561860' => array(
                    '1' => [13069983099],
                    '2' => [13030038583]
                ),
                '13313197772' => array(
                    '1' => [13199316166,13045271289],
                    '2' => [17532528272]
                ),
                '13045271289' => array(
                    '1' => [15663379689,13936021657],
                    '2' => [15846228444,13946092293,13836361328]
                ),
                '13836361328' => array(
                    '1' => [15568354931,15946245008,18714327801,13845267316,15046304106,13843241113,19989620666,18946228656]
//                ,
//                    '2' => [18714396579]
                ),
                '13936021657' => array(
                    '1' => [19904679090,15776383444],
                    '2' => [15946140988,19204651315]
                ),
                '15776383444' => array(
                    '1' => [13936116611,13945168101,15049909336,13199319002,13199261951],
                    '2' => [13030082707,13936393964,18004510760,18345750444,15546683297,15045438005,18646593300,18686762444,15945190908,18845099319]
                )
            );
//            [13846986819] => 2
//            [13811001579] => 2
//            [13528754313] => 2
//            [13946638197] => 2
//            $act = 'duplicated';
            $counts = 0;
            $act = 'findLast';
            if($act == 'duplicated') {
                $frequencies = [];
                foreach($users as $k=>$v){
                    foreach($v as $k1=>$v1){
                        foreach($v1 as $k2=>$v2){
                            echo $k."===>".$k1."===>".$k2."===>".$v2."\n";
                            if (isset($frequencies[$v2])) {
                                $frequencies[$v2]++;
                            } else {
                                // Otherwise, add it to the array with a count of 1
                                $frequencies[$v2] = 1;
                            }
                        }
                    }
                }
                arsort($frequencies);
                print_r($frequencies);
            }
            if($act == 'findLast'){
                foreach($users as $k=>$v){
                    foreach($v as $k1=>$v1){
                        foreach($v1 as $k2=>$v2){
//                            echo $v2;
                            $user = model('app\common\model\User')->where('mobile', $v2)->find();
                            if($user && $user->pid > 0 && $user->pnums1 == 0 && $user->pnums2 == 0){
                                echo $user->id."===>".$user->mobile."===>".$user->pid."===>".$user->pnet."===>".$user->pnums1."===>".$user->pnums2."\n";
                            }
                            if($user && $user->pid > 0) $counts++;
//                            else{
//                                echo "Not Found";
//                            }
//                            echo "\n";
                        }
                    }
                }
            }
            echo $counts."\n";
        }
        if($params['action'] == 'Test') {
            echo "test22";
//            $this->checkOrder();
        }
    }
    public function checkOrder(){
        $count = 0;
        foreach(model('app\api\model\wanlshop\PaySync')->where('blc','>','0')->select() as $order) {
            $user = model('app\common\model\User')->where('id', $order->user_id)->find();
            if($user->not_any == '0') {
                $count++;
                echo $order->id."===>".$order->user_id."\n";
                model('app\common\model\User')->where('id', $order->user_id)->update(['not_any' => '1']);
            }
        }
        foreach(model('app\api\model\wanlshop\Pay')->where('blc', '>','0')->select() as $order) {
            $user = model('app\common\model\User')->where('id', $order->user_id)->find();
            if($user->not_any == '0') {
                $count++;
                echo $order->id."===>".$order->user_id."\n";
                model('app\common\model\User')->where('id', $order->user_id)->update(['not_any' => '1']);
            }
        }
        echo $count."\n";
    }

    public function readJsonFile(){
        $file = 'public/z.json';
        if (file_exists($file)) {
            $content = file_get_contents($file);
            $array = json_decode($content, true); // decode为数组而不是对象
            if (is_array($array)) {
                $this->userBaseJson($array['rootData']);
//                    print_r($array);  // 输出数组内容
            } else {
                echo "无效的JSON格式";
            }
        } else {
            echo "文件不存在";
        }
    }
    public function userBaseJson($users, $lv=1){
        foreach($users as $k=>$v){
            if($k == 'title') {
                echo $lv.':User';
                for($i = 1;$i <= $lv;$i++){
                    echo "===";
                }
                echo '>';
                $user = model('app\common\model\User')->where('mobile', $v)->find();
                if($user) {
                    echo "ID:".$user->id."==>".$user->username."==>".$user->mobile;
                    echo "==>PID:".$user->pid."==>NET:".$user->pnet;
                    if($user->pid > 0) {
                        $this->userArray[] = $user->id;
                        $this->userNumbers++;
                    }
                }
//                echo $v . "\n";
                echo "\n";
            }
            if($k == 'children'){
                if($v) {
                    foreach ($v as $us) {
                        $this->userBaseJson($us, $lv + 1);
                    }
                }
            }
        }
    }
    public function findTeam($uid, $lv = 1){
        echo $lv.':User';
        for($i = 1;$i <= $lv;$i++){
            echo "===";
        }
        echo '>';
        $user = model('app\common\model\User')->where('id', $uid)->find();
        if($user) {
            echo "ID:".$user->id."==>".$user->username."==>".$user->mobile;
            echo "==>PID:".$user->pid."==>NET:".$user->pnet;
            $this->userNmNumbers++;
        }
        echo "\n";
        foreach(model('app\common\model\User')
                    ->where('pid', $uid)
                    ->order('pnet asc')
                    ->select() as $userD){
            $this->findTeam($userD->id, $lv + 1);
        }
    }
}

