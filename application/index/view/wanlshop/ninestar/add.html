<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
	<div class="alert alert-warning-light"> 温馨提示：商品详情会自动加载店铺所有优惠券，客户端用户中心仅推荐 [可用范围] 为 [全部商品] 详情 </div>
	<div id="app" v-cloak>
		<div class="form-group">
			<label class="control-label col-xs-12 col-sm-2">{:__('Type')}:</label>
			<div class="col-xs-12 col-sm-8">
				<select id="c-type" data-rule="required" class="form-control selectpicker" name="row[type]" v-model="type">
					{foreach name="typeList" item="vo"}
						<option value="{$key}" {in name="key" value="ninestar"}selected{/in}>{$vo}</option>
					{/foreach}
				</select>
			</div>
		</div>
		
		<div class="form-group">
			<label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
			<div class="col-xs-12 col-sm-8">
				<input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="">
			</div>
		</div>

		<block v-if="type == 'ninestar'">
			<div class="form-group">
				<label class="control-label col-xs-12 col-sm-2">{:__('Price')}:</label>
				<div class="col-xs-12 col-sm-8">
					<input id="c-price" class="form-control" step="1" name="row[price]" type="number" value="0.00">
				</div>
			</div>
		</block>
		<block v-if="type == 'dragon'">
			<div class="form-group">
				<label class="control-label col-xs-12 col-sm-2">{:__('BackRule')}:</label>
				<div class="col-xs-12 col-sm-8">
					<select id="c-back-rule" data-rule="required" @change="selectChange" class="form-control selectpicker" name="row[back_rule]" v-model="backRule">
						{foreach name="getBackRuleList" item="vo"}
						<option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
						{/foreach}
					</select>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-xs-12 col-sm-2">{:__('Pv Value')}:</label>
				<div class="col-xs-12 col-sm-8">
					<input data-key="backRule" data-rule="required" class="form-control" step="0.01" name="row[pv_value]" :min="pv" :value="pv" type="number" placeholder="输入让利值">
				</div>
			</div>
		</block>
		<div class="form-group">
			<label class="control-label col-xs-12 col-sm-2">{:__('Rangetype')}:</label>
			<div class="col-xs-12 col-sm-8">
							
				<select @change="rangeChange" id="c-rangetype" data-rule="required" class="form-control selectpicker" name="row[rangetype]" v-model="rangetype">
					{foreach name="rangetypeList" item="vo"}
						<option value="{$key}" {in name="key" value="goods"}selected{/in}>{$vo}</option>
					{/foreach}
				</select>

			</div>
		</div>
		<block v-if="rangetype == 'goods' || rangetype == 'category'">
			<div class="form-group">
				<label class="control-label col-xs-12 col-sm-2">{{rangename[rangetype]}}:</label>
				<div class="col-xs-12 col-sm-8">
					<div class="input-group" v-if="rangetype == 'goods'" @click="goodsLink">
						<input id="c-range" data-rule="required" class="form-control" type="text" v-model="range" placeholder="请选择商品" disabled="disabled">
						<input name="row[range]" type="hidden" v-model="range">
						<div class="input-group-addon no-border" style="padding: 0; padding-left: 6px;">
							<span>
								<div type="button" class="btn btn-primary"><i class="fa fa-list"></i> 选择商品</div>
							</span>
						</div>
					</div>
					<div class="input-group" v-else  @click="categoryLink">
						<input id="c-range" data-rule="required" class="form-control" type="text" v-model="range" placeholder="请选择商家类目" disabled="disabled">
						<input name="row[range]" type="hidden" v-model="range">
						<div class="input-group-addon no-border" style="padding: 0; padding-left: 6px;">
							<span>
								<div type="button" class="btn btn-primary"><i class="fa fa-list"></i> 选择商家类目</div>
							</span>
						</div>
					</div>
				</div>
			</div>
		</block>
		<block v-if="type == 'love'">
			<div class="form-group">
				<label class="control-label col-xs-12 col-sm-2">{:__('integraltype')}:</label>
				<div class="col-xs-12 col-sm-8">
					<select id="c-integraltype" data-rule="required" class="form-control selectpicker" name="row[integraltype]">
						{foreach name="integraltype" item="vo"}
							<option value="{$key}" {in name="key" value="ninestar"}selected{/in}>{$vo}</option>
						{/foreach}
					</select>
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-xs-12 col-sm-2">{:__('integral')}:</label>
				<div class="col-xs-12 col-sm-8">
					<input id="c-integral" class="form-control" step="1" name="row[integral]" type="number" v-model="integral">
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-xs-12 col-sm-2"></label>
				<div class="col-xs-12 col-sm-8">
					<p class="text-danger">{{goodmsg}}</p>
					<p class="text-danger">提示：积分数值相当于产品的价值的30%，产品价格是产品价值的70%</p>
				</div>
			</div>
		</block>
		<block v-if="type != 'vip'">
			<div class="form-group">
				<label class="control-label col-xs-12 col-sm-2">{:__('Pretype')}:</label>
				<div class="col-xs-12 col-sm-8">
					<select  id="c-pretype" data-rule="required" class="form-control selectpicker" name="row[pretype]" v-model="pretype">
						{foreach name="pretypeList" item="vo"}
							<option value="{$key}" {in name="key" value="appoint"}selected{/in}>{$vo}</option>
						{/foreach}
					</select>
				</div>
			</div>
		</block>
		<block v-if="pretype == 'appoint'">
			<div class="form-group">
				<label class="control-label col-xs-12 col-sm-2">{:__('Validity')}:</label>
				<div class="col-xs-12 col-sm-8">
					<input id="c-validity" class="form-control" name="row[validity]" type="number" value="0">
					<p class="text-gray" style="margin-top: 5px;">温馨提示：设置为0标识不会过期</p>
				</div>
			</div>
		</block>
		

		<block v-show="pretype == 'fixed'">
			<div class="form-group">
				<label class="control-label col-xs-12 col-sm-2">{:__('Startdate')}:</label>
				<div class="col-xs-12 col-sm-8">
					<input id="c-startdate" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[startdate]" type="text" value="{:date('Y-m-d')}">
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-xs-12 col-sm-2">{:__('Enddate')}:</label>
				<div class="col-xs-12 col-sm-8">
					<input id="c-enddate" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[enddate]" type="text" value="{:date('Y-m-d')}">
				</div>
			</div>
		</block>
		<div class="form-group">
			<label class="control-label col-xs-12 col-sm-2">{:__('Drawlimit')}:</label>
			<div class="col-xs-12 col-sm-8">
				<input id="c-drawlimit" data-rule="required" class="form-control" name="row[drawlimit]" type="number" value="0">
				<p class="text-gray" style="margin-top: 5px;">温馨提示：设置为0则不限领取数量</p>
			</div>
		</div>
		<div class="form-group">
			<label class="control-label col-xs-12 col-sm-2">{:__('Content')}:</label>
			<div class="col-xs-12 col-sm-8">
				<textarea id="c-content" class="form-control editor" rows="5" name="row[content]" cols="50"></textarea>
			</div>
		</div>
		<div class="form-group layer-footer">
			<label class="control-label col-xs-12 col-sm-2"></label>
			<div class="col-xs-12 col-sm-8">
				<button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
				<button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
			</div>
		</div>
	</div>
</form>
