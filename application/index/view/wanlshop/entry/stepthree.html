<link rel="stylesheet" href="__CDN__/assets/addons/wanlshop/css/entry.css">
<div id="content-container" class="container">
	<div class="row">
		<form class="form-horizontal" role="form" data-toggle="validator" method="POST">
			<div class="wanl-entry col-md-12">
				<div class="head">
					<div class="title">
						<p>商城申请入驻</p>
					</div>
					<div class="entry-content">
						<ol class="ui-step ui-step-4">
							<li class="step-start step-done">
								<div class="ui-step-line"></div>
								<div class="ui-step-cont">
									<span class="ui-step-cont-number">1</span>
									<span class="ui-step-cont-text">注册账号</span>
								</div>
							</li>
							<li class="step-start step-done">
								<div class="ui-step-line"></div>
								<div class="ui-step-cont">
									<span class="ui-step-cont-number">2</span>
									<span class="ui-step-cont-text">主体资质</span>
								</div>
							</li>
							<li class="step-active">
								<div class="ui-step-line"></div>
								<div class="ui-step-cont">
									<span class="ui-step-cont-number">3</span>
									<span class="ui-step-cont-text">店铺信息</span>
								</div>
							</li>
							<li class="step-end">
								<div class="ui-step-line"></div>
								<div class="ui-step-cont">
									<span class="ui-step-cont-number">4</span>
									<span class="ui-step-cont-text">提交审核</span>
								</div>
							</li>
						</ol>
					</div>
				</div>
				<div>
					<div class="title-container">
						<span class="title">填写店铺信息</span>
					</div>
					<div class="entry-content">
						<div class="form-group">
							<label class="control-label col-xs-12 col-sm-2">{:__('店铺名')}:</label>
							<div class="col-xs-12 col-sm-8">
								{:Form::text('shopname', $entry.shopname, ['data-rule'=>'required'])}
							</div>
						</div>
						<div class="form-group">
						    <label class="control-label col-xs-12 col-sm-2">{:__('店铺图标')}:</label>
						    <div class="col-xs-12 col-sm-8">
						        <div class="input-group">
						            <input id="c-avatar" data-rule="required" class="form-control" size="50" name="avatar" type="text" value="{$entry.avatar}">
						            <div class="input-group-addon no-border no-padding">
						                <span><button type="button" id="plupload-avatar" class="btn btn-danger plupload" data-input-id="c-avatar" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-avatar"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
						            </div>
						            <span class="msg-box n-right" for="c-avatar"></span>
						        </div>
						        <ul class="row list-inline plupload-preview" id="p-avatar"></ul>
						    </div>
						</div>
						<div class="form-group">
							<label class="control-label col-xs-12 col-sm-2">{:__('店铺简介')}:</label>
							<div class="col-xs-12 col-sm-8">
								{:Form::textarea('bio', $entry.bio, ['data-rule'=>'required'])}
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-xs-12 col-sm-2">{:__('经营地址')}:</label>
							<div class="col-xs-12 col-sm-8">
								{:Form::citypicker('city', $entry.city, ['data-rule'=>'required'])}
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-xs-12 col-sm-2">{:__('店铺介绍')}:</label>
							<div class="col-xs-12 col-sm-8">
								{:Form::editor('content', $entry.content, ['data-rule'=>'required'])}
							</div>
						</div>
					</div>
				</div>
				<div align="center">
					<div class="entry-content">
						<a href="/index/wanlshop/entry.html" class="btn btn-default btn-lg">上一步</a>
						<button type="submit" class="btn btn-success btn-lg">提交审核</button>
					</div>
				</div>
			</div>
		</form>
	</div>
</div>
