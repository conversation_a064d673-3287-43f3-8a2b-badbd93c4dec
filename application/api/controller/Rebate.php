<?php

namespace app\api\controller;

use app\common\controller\Api;

/**
 * 分红
 */
class Rebate extends Api
{

    protected $noNeedRight = ['*'];

    /**
     * 列表
     * @return void
     */
    public function lists(){
        if($this->request->isPost()){
            $type = $this->request->post('type');
            if(!$type){
                $this->error('缺少必要参数');
            }
            if($type == 'mch'){
                $rebateLog = model('app\common\model\StoreRebateLog');
            } else if($type == 'user'){
                $rebateLog = model('app\common\model\UserRebateLog');
            } else{
                $this->error('类型有误');
            }
            $lists = $rebateLog->where(['user_id'=>$this->auth->id,'status'=>['<','3']])->order('id desc')->select();
            $this->success('',$lists);
        }
        $this->error('非法操作');
    }

    /**
     * 全部积分all_points
     * 待入池wait_join
     * 已入池already_join
     * 已获得抵用券already_xfq
     * 待加速wait_expedite
     * 最高得券额max_xfq
     * 分享加速split_expedite
     * 数商加速ss_expedite
     * 一级加速top_expedite
     * @return void
     */
    public function info(){
        $rebateLog = model('app\common\model\UserRebateLog');
        $sum_count = $rebateLog
            ->where(['status'=>['<',3],'user_id'=>$this->auth->id])
            ->field('
            IFNULL(sum(points),0) as all_points,
            IFNULL(sum(fh),0) as already_xfq,
            IFNULL(sum(js),0) as wait_expedite,
            IFNULL(sum(points*cap),0) as max_xfq,
            IFNULL(sum(split),"0.00") as split_expedite
            ')
            ->find()->toArray();
        $this->success('',$sum_count + [
            'all_points'=>'0.00',
            'wait_join'=>number_format($rebateLog
                ->where(['status'=>['<',2],'user_id'=>$this->auth->id])->sum('points'),2),
            'already_join'=>number_format($rebateLog
                ->where(['status'=>2,'user_id'=>$this->auth->id])->sum('points'),2),
            'already_xfq'=>'0.00',
            'wait_expedite'=>'0.00',
            'max_xfq'=>'0.00',
            'split_expedite'=>'0.00',
            'ss_expedite'=>'0.00',
            'top_expedite'=>'0.00',
        ]);
    }



}
