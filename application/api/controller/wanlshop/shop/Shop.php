<?php

namespace app\api\controller\wanlshop\shop;

use app\common\controller\Api;
use think\Db;
use Exception;
use think\exception\PDOException;
use think\exception\ValidateException;

use fast\Tree;

/**
 * 店铺管理
 * @internal
 */
class Shop extends Api
{
    protected $noNeedLogin = '';
    protected $noNeedRight = '*';
    /**
     * Shop模型对象
     */
    protected $model = null;
    
    public function _initialize()
    {
        parent::_initialize();
        
        
        $this->model = new \app\api\model\wanlshop\shop\Shop;
//        $this->view->assign("stateList", $this->model->getStateList());
//        $this->view->assign("statusList", $this->model->getStatusList());
//        $this->view->assign("typeList", $this->model->getTypeList());
//        $tree = Tree::instance();
//        $category = new \app\api\model\wanlshop\shop\Category;// 类目
//        $tree->init(collection($category->where(['type' => 'goods'])->order('weigh desc,id desc')->field('id,pid,type,name,name_spacer')->select())->toArray(), 'pid');
//        $this->assignconfig('pageCategory', $tree->getTreeList($tree->getTreeArray(0), 'name_spacer'));
    }
    
    /**
     * 类目管理
     */
    public function index()
    {
        return $this->view->fetch('wanlshop/page/index');
    }
	
    /**
     * 品牌管理
     */
    public function brand()
    {
		$this->view->assign("stateList", model('app\index\model\wanlshop\Brand')->getStateList());
        return $this->view->fetch('wanlshop/brand/index');
    }
    
    /**
     * 店铺资料
     */
    public function profile()
    {
        $row = model('app\api\model\wanlshop\Shop')
            ->where(['user_id' => $this->auth->id])
            ->find();
        if (!$row) {
            $this->error(__('店铺未开通！'));
        }
        if ($this->request->isPost()) {
            $pData = $this->request->post();
            $shoparr = array();
//            $shoparr['user_id'] = $this->auth->id;
//            $shoparr['state'] = '0';
            isset($pData['shopname']) && $shoparr['shopname'] = $pData['shopname'];
            isset($pData['avatar']) && $shoparr['avatar'] = $pData['avatar'];
            isset($pData['bio']) && $shoparr['bio'] = $pData['bio'];
            isset($pData['description']) && $shoparr['description'] = $pData['description'];
            isset($pData['city']) && $shoparr['city'] = $pData['city'];
//          $shoparr['verify'] = '3';
            
            //云店管理订单统计
            $money = model('app\api\model\wanlshop\OrderGoods')
            ->alias('goods')
            ->where(['goods.shop_id'=> $row['id']])
            ->where('good.activity_type','<>','dragon')
            ->whereIn('orders.state', [2,3,4,5,6])
            ->whereTime('goods.createtime','year')
            ->field('sum(goods.actual_payment) as money')
            ->join('fa_wanlshop_order orders', "orders.id = goods.order_id", 'LEFT')
            ->join('fa_wanlshop_goods good', "good.id = goods.goods_id", 'LEFT')
            ->find();

            $number = model('app\api\model\wanlshop\OrderGoods')
            ->alias('goods')
            ->where(['goods.shop_id'=> $row['id']])
            ->where('good.activity_type','<>','dragon')
            ->whereIn('orders.state', [2,3,4,5,6])
            ->whereTime('goods.createtime','year')
            ->field('count(DISTINCT orders.user_id) as peoplenumber,count(orders.id) ordernumber')
            ->join('fa_wanlshop_order orders', "orders.id = goods.order_id", 'LEFT')
            ->join('fa_wanlshop_goods good', "good.id = goods.goods_id", 'LEFT')
            ->find();

            $hmoney = model('app\api\model\wanlshop\OrderGoods')
            ->alias('goods')
            ->where(['goods.shop_id'=> $row['id']])
            ->where(['good.activity_type'=> 'dragon'])
            ->whereIn('orders.state', [2,3,4,5,6])
            ->whereTime('goods.createtime','year')
            ->field('sum(goods.actual_payment) as money')
            ->join('fa_wanlshop_order orders', "orders.id = goods.order_id", 'LEFT')
            ->join('fa_wanlshop_goods good', "good.id = goods.goods_id", 'LEFT')
            ->find();

            $hnumber = model('app\api\model\wanlshop\OrderGoods')
            ->alias('goods')
            ->where(['goods.shop_id'=> $row['id']])
            ->where(['good.activity_type'=> 'dragon'])
            ->whereIn('orders.state', [2,3,4,5,6])
            ->whereTime('goods.createtime','year')
            ->field('count(DISTINCT orders.user_id) as peoplenumber,count(orders.id) ordernumber')
            ->join('fa_wanlshop_order orders', "orders.id = goods.order_id", 'LEFT')
            ->join('fa_wanlshop_goods good', "good.id = goods.goods_id", 'LEFT')
            ->find();

            $row['teams'] = [
                'self'=>[
                    'money'=>$money['money']??0,
                    'ordernumber'=>$number['ordernumber']??0,
                    'peoplenumber'=>$number['peoplenumber']??0,
                ],
                'help'=>[
                    'money'=>$hmoney['money']??0,
                    'ordernumber'=>$hnumber['ordernumber']??0,
                    'peoplenumber'=>$hnumber['peoplenumber']??0,
                ],
            ];

            model('app\api\model\wanlshop\shop')->where('user_id', $this->auth->id)->update($shoparr);
        }
        $this->success('返回成功', $row);
    }
    
    /**
     * 图片空间
     */
    public function attachment()
    {
        $attachment = model('Attachment');
        $this->view->assign("picCount", $attachment->where('user_id', $this->auth->id)->count());
        $size = $attachment->where('user_id', $this->auth->id)->sum('filesize');
        $units = array('K','Kb','M','G','T');
        $i = 0;
        for (; $size>=1024 && $i<count($units); $i++) {
            $size /= 1024;
        }
        $this->view->assign("picSum", round($size, 2).$units[$i]);
		$this->view->assign("mimetypeList", \app\common\model\Attachment::getMimetypeList());
        return $this->view->fetch('wanlshop/attachment/index');
    }
    
    /**
     * 类目管理
     */
    public function category()
    {
        return $this->view->fetch('wanlshop/shopsort/index');
    }
    
    /**
     * 服务
     */
    public function service()
    {
        if ($this->request->isGet()) {
            $where['status'] = 'normal';
            $total = model('app\index\model\wanlshop\ShopService')->where($where)->count();
            $list = model('app\index\model\wanlshop\ShopService')->where($where)->select();
            $list = collection($list)->toArray();
            $result = array("total" => $total, "rows" => $list);
//            return json($result);
            $this->success('ok', $result);
        }
    }
	
}
