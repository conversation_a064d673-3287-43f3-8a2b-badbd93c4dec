<?php

namespace app\api\model\wanlshop;

use think\Model;

use function fast\array_get;

class PayOrder extends Model
{
	// 表名
	protected $name = 'pay_order';
	// 开启自动写入时间戳字段
	protected $autoWriteTimestamp = 'int';
	// 定义时间戳字段名
	protected $createTime = 'createtime';
	protected $updateTime = 'updatetime';

	// 追加属性
	protected $append = [
		'createtime_text',
	    'paytime_text',
	    'status_text',
	];

	public function getStatusList()
    {
        return ['created' => __('待支付'), 'paid' => __('已支付'), 'expired' => __('已取消'), 'refund' => __('已退款')];
    }

	public function getCreatetimeTextAttr($value, $data)
	{
	    $value = $value ? $value : (isset($data['createtime']) ? $data['createtime'] : '');
	    return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
	}

	public function getPaytimeTextAttr($value, $data)
	{
	    $value = $value ? $value : (isset($data['paytime']) ? $data['paytime'] : '');
	    return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
	}

	public function getStatusTextAttr($value, $data)
	{
	    $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
	    return array_get($this->getStatusList(),$value,'未知');
	}

	// 店铺
	public function shop()
	{
	    return $this->belongsTo('app\api\model\wanlshop\Shop', 'shop_id', 'id', [], 'LEFT')->setEagerlyType(0);
	}
}