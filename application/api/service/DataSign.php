<?php


namespace app\api\service;


use fast\Rsa;

class DataSign
{
    private $rsa;
    public $publicKey = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgJ3gWwYzI+SbEjtuqS3kNlJQ/KNYWcfRB044HFOoiXIjDSoXR+lvy3L3GE8nrv22ynbplnVIpaElKAu5wFAtST70O2y5snHc/5fTVnKwQM+NzrqdDbpP3dHrXVYVOBwnb3tH6fRzu35I0xI3GDccUd+AMHkiEqJCAYI+3EYyjAtAXW2U4eL8FRsCqZg2JWpGviOOnxrLkQJc748gHw0zEJz+MG4ZsDcdiialEqzT0jN3JFiIJ7hoF92BVFKz4joyl3UjN+zhloMNXD3cAhDR/n759DQ8FJGcMvSAQkL+kclunZzp93vjbFcMbfYmaHVgRjj8GZkuxpZO5s4SepZopQIDAQAB';
    public $privateKey = 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCAneBbBjMj5JsSO26pLeQ2UlD8o1hZx9EHTjgcU6iJciMNKhdH6W/LcvcYTyeu/bbKdumWdUiloSUoC7nAUC1JPvQ7bLmycdz/l9NWcrBAz43Oup0Nuk/d0etdVhU4HCdve0fp9HO7fkjTEjcYNxxR34AweSISokIBgj7cRjKMC0BdbZTh4vwVGwKpmDYlaka+I46fGsuRAlzvjyAfDTMQnP4wbhmwNx2KJqUSrNPSM3ckWIgnuGgX3YFUUrPiOjKXdSM37OGWgw1cPdwCENH+fvn0NDwUkZwy9IBCQv6RyW6dnOn3e+NsVwxt9iZodWBGOPwZmS7Glk7mzhJ6lmilAgMBAAECggEACTlJSb3cFQrkBB63BPMBltO4cyXTxDyMRRXa+E0XnSPOV7dZDBM1rUWFavziP656WXLFtcCbgSVZYNHWdHBLjyEwqsJVXvLDgqEEywJWmY9JADZp5P4jlCjl1D/ELc33FgulUKXzVtLrrhd0VAw/v69jphZJ10RDr5ADqhBut5RMowoYbT19G7dSFoOIgmhhHGPg+PgLP5LZ+szKiGOw0GSV3J8cX3VPPf+X0RZBszj7G2Vu96pmdJgXLkWU9kKz8nRgLv/ZgbodNAM717AZYB0Hbvo2kIHWAgubMjZyL4xP6iHmRx91oweCiUs6Nrj5PwYsCwXMV+3TIxF2cFNwgQKBgQDUjzL5QxNmjKuyXpKl4WCC2PNjxnu5oFdv7zJ0Ih2Z1KsRNWUb/bPIaEDiSq8Jd3XPke1LSlPk/gGjD+ylziHlZBrgsWAiy4IC047r/hbEyREdIRLHVK4FYawK1V705c3SOrq2+zgAbc8B+y4bpAdtAzALgf2NSYrwjjxT5UpRyQKBgQCa5urlTCIHoecLkDeCbyDiyo3HO3rkNS7fL31Hs9Pc/OdYU3A1VSwJWbx774f80f0le0uEfCWmO7VRLNOvv5QmkvsfszY47vlBii9FsrarzuXvV/FwrgBt3S2XwAdPt9RsUjn6t/n8kVCdr7CJ5gauMkpaMfqoXcVDt4dsIUlt/QKBgQCgsEDQvyyDcgS6q1QhoufbAze8TehqxBRVwnZZ5RCLooJQWBU/u+OVtXqXl+4LMgwWBzNkn6QckQmbxAYasmJdj/fhtTYAF+kJSfXZ4hREX9xYMRCJMDL+ToBJEFTgvnmCXHOasP/9EwZTcRfCPZZ/itFfYb3UeaxXfq+MnOI6QQKBgHa/DtM4yd2P/86df/A9wLgbDxTsRAktvADcJu4QrRl7xt2vGRXJdTi10ggwV884KxfQrNOjWDlXDGhuNNlFxAzOIDTs7WQ3zlyv0g1cMy0CLsKMaEo5GZsfMxRh4Tj7c5yWbFwHLVyxCVvYkz0jWXuwkc8TAVSQjeibiPUbj28BAoGAGnojOytN6qTOVzcR9ZsCx5Yjxtv6PSQhq6JDyvMqCYMN7pO2MWxJujmUSRQlKVOJzU2tuhxgIX3ScKmNVX0IaYn2i3rcYVCizHPDG4VKxHhjtzElHmSMiumh8/JVWKnQ9EeQjuQwQtRHLmWJEjsEvbKcwzeCZuxd4tl6I8d/bDw=';

    public function __construct()
    {
        $this->rsa = new Rsa($this->publicKey,$this->privateKey);
    }

    public function sign($params)
    {
        ksort($params);
        $str = md5(serialize($params));
        $sign = $this->rsa->sign($str);
        $params['sign'] = $sign;
        return $params;
    }


    public function verify($params)
    {
        $signStr = $params['sign'];
        unset($params['sign']);
        ksort($params);
        $str = md5(serialize($params));
        return $this->rsa->verify($str, $signStr);
    }

}
