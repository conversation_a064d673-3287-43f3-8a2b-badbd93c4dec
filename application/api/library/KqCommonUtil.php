<?php
namespace app\api\library;

class KqCommonUtil{
    public static function moveImagesAndConventToBASE64(array $frontImageFile){
        if($_FILES){
            $frontImageFile_name = $frontImageFile['name'];
            $tmp_images = $frontImageFile['tmp_name'];
            $error = $frontImageFile['error'];
            if($error == 0){
                //将图片放入images文件夹
                move_uploaded_file($tmp_images, 'images/'.$frontImageFile_name);
                //开始转Base64并返回数据
                $localImagePath ='images/'.$frontImageFile_name;
                $image_data = fread(fopen($localImagePath, 'r'), filesize($localImagePath));
                $base64_image = chunk_split(base64_encode($image_data));
                return $base64_image;
            }
        }
    }

    public static function kq_curl($url,$str){
        $user_agent = "Mozilla/4.0 (compatible; MSIE 5.01; Windows NT 5.0)";
        $header[] = "Content-type: application/json;charset=utf-8";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL,$url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST,2);	
        curl_setopt($ch, CURLOPT_USERAGENT,$user_agent);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER,false);
        curl_setopt($ch, CURLOPT_SSLCERTTYPE, 'P12');
        curl_setopt($ch, CURLOPT_POSTFIELDS, $str);
        $output = curl_exec($ch);
        $httpCode = curl_getinfo($ch,CURLINFO_HTTP_CODE);
        if($httpCode!=200){
            return ['code'=>0,'msg'=>'curl_error:'.curl_error($ch)];
        }
        return ['code'=>1,'msg'=>'success','data'=>$output];
    }
}