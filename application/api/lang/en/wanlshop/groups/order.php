<?php
    return [
        '网络繁忙' => 'Network busy',
        '发送成功' => 'Sent successfully',
        '非法请求' => 'Illegal request',
        '订单异常' => 'Order exception',
        '已经修改过一次了' => 'It has been modified once',
        '系统繁忙，请稍后再试！' => 'System busy, please try again later!',
        '页面安全令牌已过期！请重返此页' => 'Page security token has expired! Please return to this page',
        '请点击上方添加收货地址' => 'Please click above to add shipping address',
        '订单繁忙ERR001：请返回商品详情重新提交订单' => 'Order busy ERR001: Please return to product details and resubmit order',
        '订单繁忙ERR002：请返回商品详情重新提交订单' => 'Order busy ERR002: Please return to product details and resubmit order',
        '地址异常，没有找到该地址' => 'Address exception, this address was not found',
        '网络繁忙，创建订单失败！' => 'Network busy, order creation failed!',
        '订单异常，请返回重新下单' => 'Order exception, please return to place a new order',
        '对不起当前商品不存在或已下架！' => 'Sorry, the current product does not exist or has been taken off the shelf!',
        '网络异常SHOPID错误！' => 'Network exception, SHOPID error!',
        '系统繁忙,请重试！' => 'System busy, please try again!',
        '查询团购阶梯失败' => 'Failed to query group buying ladder',
        '参与拼单失败，因选择的团' => 'Failed to join group order because the selected group',
        '准备中' => 'Preparing',
        '已成团' => 'Group formed',
        '拼团关闭' => 'Group buying closed',
        '参与拼单失败，不可拼自己的团' => 'Failed to join group order, cannot join your own group',
        '参与拼单失败，因拼团已完成' => 'Failed to join group order because the group has been completed',
        '订单暂时不支持单独购买' => 'The order does not currently support individual purchase',
        '非法访问' => 'Illegal access',
        '付款后，完成拼团即可将宝贝发出' => 'After payment, complete the group buying to ship the product',
        '尚未付款' => 'Not yet paid',
        '正在分享拼团中' => 'Sharing group buying in progress',
        '已付款' => 'Paid',
        '商家正在处理订单' => 'Merchant is processing the order',
        '已发货' => 'Shipped',
        '包裹正在等待快递小哥揽收~' => 'Package is waiting for courier pickup~',
        '商家接受到您的订单，准备出库' => 'Merchant received your order, preparing for shipment',
        '已下单' => 'Order placed',
        '打包完成，正在等待快递小哥揽收~' => 'Packing completed, waiting for courier pickup~',
        '仓库处理中' => 'Warehouse processing',
        '此商品每个ID仅可购买' => 'Each ID can only purchase',
        '件，你已购买过' => 'pieces of this product, you have already purchased',
        '件' => 'pieces',
        '订单金额' => 'Order amount',
        '元，不满' => 'yuan, less than',
        '元' => 'yuan',
        '服务器不支持Redis，请安装Redis和php redis拓展' => 'Server does not support Redis, please install Redis and php redis extension',
        '或未在后台配置Redis' => 'or Redis is not configured in the background',
        '付款后，即可将宝贝发出' => 'After payment, the product can be shipped',
        '付款后，完成拼团即可将宝贝发出' => 'After payment, complete the group buying to ship the product',
        '已经评论过或订单异常' => 'Already commented or order exception',
        '拼团应用订单接口' => 'Group Buying Application Order Interface',
        '获取拼团订单列表' => 'Get Group Buying Order List',
        '查询用户店铺订单记录' => 'Query User Store Order Records',
        '获取订单详情' => 'Get Order Details',
        '取消订单' => 'Cancel Order',
        '删除订单' => 'Delete Order',
        '修改地址' => 'Modify Address',
        '确认收货' => 'Confirm Receipt',
        '评论订单' => 'Comment Order',
        '获取订单物流状态' => 'Get Order Logistics Status',
        '查询购买次数限制' => 'Query Purchase Limit',
        '确认拼团订单' => 'Confirm Group Buying Order',
        '提交订单' => 'Submit Order',
        '订单状态码（方法内使用）' => 'Order Status Code (for internal use)',
        '拼团 获取优惠券后金额 内部方法' => 'Group Buying - Get Amount After Coupon (Internal Method)',
        '获取运费模板和子类 内部方法' => 'Get Freight Template and Subclasses (Internal Method)',
        'Redis连接' => 'Redis Connection',
        '创建Token' => 'Create Token',
        '验证Token' => 'Verify Token',
    ];