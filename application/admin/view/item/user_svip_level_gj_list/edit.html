<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Pid')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-pid" class="form-control" name="row[pid]" type="number" value="{$row.pid|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-type" class="form-control selectpicker" name="row[type]">
                {foreach name="typeList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.type"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Lv')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-lv" class="form-control selectpicker" name="row[lv]">
                {foreach name="lvList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.lv"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Cost')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-cost" class="form-control" name="row[cost]" type="number" value="{$row.cost|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('MoneyA')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-moneyA" class="form-control" name="row[moneyA]" type="number" value="{$row.moneyA|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('MoneyB')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-moneyB" class="form-control" name="row[moneyB]" type="number" value="{$row.moneyB|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('NumsA')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-numsA" class="form-control" name="row[numsA]" type="number" value="{$row.numsA|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('NumsB')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-numsB" class="form-control" name="row[numsB]" type="number" value="{$row.numsB|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Permi')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-permi" class="form-control selectpicker" name="row[permi]">
                {foreach name="permiList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.permi"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('NumsS')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-numsS" class="form-control" step="0.01" name="row[numsS]" type="number" value="{$row.numsS|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Bo')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-bo" class="form-control" name="row[bo]" type="number" value="{$row.bo|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
