<style type="text/css">
	.content {
		padding: 25px 20px;
	}

	/* 订单详情 */
	.order-detail .order-block {
		height: 31px;
		line-height: 31px;
		background: #e8edf0;
		border-radius: 13px;
		font-size: 14px;
		text-align: center;
		position: relative;
		margin-bottom: 50px;
	}

	.order-detail .order-block:before,
	.order-detail .order-block:after {
		content: "";
		position: absolute;
		z-index: 2;
		left: 0;
		top: 0;
		bottom: 0;
		border-radius: 13px;
		background: #18bc9c;
	}

	.order-detail .order-block:after {
		background: #4dc7af;
		z-index: 1;
	}

	.order-detail .order-block.progress-1:before {
		width: 0;
	}

	.order-detail .order-block.progress-1:after {
		width: 20%;
	}

	.order-detail .order-block.progress-2:before {
		width: 20%;
	}

	.order-detail .order-block.progress-2:after {
		width: 40%;
	}

	.order-detail .order-block.progress-3:before {
		width: 40%;
	}

	.order-detail .order-block.progress-3:after {
		width: 60%;
	}

	.order-detail .order-block.progress-4:before {
		width: 60%;
	}

	.order-detail .order-block.progress-4:after {
		width: 80%;
	}

	.order-detail .order-block.progress-5:before {
		width: 100%;
	}

	.order-detail .order-block.progress-5:after {
		width: 100%;
	}

	.order-detail .order-block.progress-5 li:nth-child(5) {
		color: #fff;
	}

	.order-detail .order-block li {
		width: 20%;
		float: left;
		list-style-type: none;
		border-radius: 13px;
		position: relative;
		z-index: 3;
	}

	.order-detail .order-block .tip {
		font-size: 12px;
		padding-top: 10px;
		color: #717171;
	}

	.order-detail .order-block.progress-1 li:nth-child(1),
	.order-detail .order-block.progress-2 li:nth-child(1),
	.order-detail .order-block.progress-3 li:nth-child(1),
	.order-detail .order-block.progress-4 li:nth-child(1),
	.order-detail .order-block.progress-5 li:nth-child(1) {
		color: #fff;
	}

	.order-detail .order-block.progress-2 li:nth-child(2),
	.order-detail .order-block.progress-3 li:nth-child(2),
	.order-detail .order-block.progress-4 li:nth-child(2),
	.order-detail .order-block.progress-5 li:nth-child(2) {
		color: #fff;
	}

	.order-detail .order-block.progress-3 li:nth-child(3),
	.order-detail .order-block.progress-4 li:nth-child(3),
	.order-detail .order-block.progress-5 li:nth-child(3) {
		color: #fff;
	}

	.order-detail .order-block.progress-4 li:nth-child(4),
	.order-detail .order-block.progress-5 li:nth-child(4) {
		color: #fff;
	}

	.order-detail .td__order-price {
		width: 200px;
		display: inline-block;
	}

	.order-head {
		width: 100%;
		padding: 12px 20px;
		margin-top: 60px;
		/* margin-bottom: 20px; */
		/* border-bottom: 1px solid #eef1f5; */
	}

	.order-head:not(:first-child) {
		margin-top: 0;
	}

	.order-head .title {
		position: relative;
		font-size: 1.5rem;
		color: #333;
	}

	.order-head .title::before {
		content: '';
		position: absolute;
		width: 4px;
		height: 14px;
		background: #18bc9c;
		top: 4px;
		left: -12px;
	}

	.table-responsive .table>tbody>tr>td {
		text-align: center;
		vertical-align: middle;
		color: #676767;
	}

	.ordertext {
		margin: 0 30px;
	}

	.table-bordered {
		border: 1px solid #e8edf0;
	}

	.table-bordered>thead>tr>th,
	.table-bordered>tbody>tr>th,
	.table-bordered>tfoot>tr>th,
	.table-bordered>thead>tr>td,
	.table-bordered>tbody>tr>td,
	.table-bordered>tfoot>tr>td {
		border: 1px solid #e8edf0;
	}

	.table thead tr {
		background: #f8f9fb;
	}

	.order_price span {
		width: 70px;
		display: inline-block;
		text-align: right;
	}
</style>
<div class="order-detail">
	<ul class="order-block progress-{$row.state}">
		<li>
			<span>下单时间</span>
			<div class="tip">{$row.createtime?datetime($row.createtime):''}</div>
		</li>
		<li>
			<span>付款</span>
			<div class="tip">{$row.paymenttime?datetime($row.paymenttime):''}</div>
		</li>
		<li>
			<span>发货</span>
			<div class="tip">{$row.delivertime?datetime($row.delivertime):''}</div>
		</li>
		<li>
			<span>收货</span>
			<div class="tip">{$row.taketime?datetime($row.taketime):''}</div>
		</li>
		<li>
			<span>完成</span>
			<div class="tip">{$row.dealtime?datetime($row.dealtime):''}</div>
		</li>
	</ul>
</div>
<div class="row">
	<div class="col-md-12">
		<div class="panel panel-default">
			<div class="panel-heading">商品信息</div>
			<div class="panel-body">
				<p><span>订单号：</span><small>{$row.order_no}</small> <span style="margin-left: 30px;">买家：</span><small>{$row.user.nickname}</small><strong></strong></p>
				<table class="table table-bordered">
					<thead>
						<tr>
							<th class="text-center">
								<div class="th-inner">商品编码</div>
							</th>
							<th class="text-center">
								<div class="th-inner">主图</div>
							</th>
							<th class="text-center">
								<div class="th-inner">商品名称</div>
							</th>
							<th class="text-center">
								<div class="th-inner">购买规格</div>
							</th>
							<th class="text-center">
								<div class="th-inner">数量</div>
							</th>
							<th class="text-center">
								<div class="th-inner">单价</div>
							</th>
							<th class="text-center">
								<div class="th-inner">总价</div>
							</th>
							<th class="text-center">
								<div class="th-inner">优惠</div>
							</th>
							<th class="text-center">
								<div class="th-inner">实际支付</div>
							</th> 
							<th class="text-center">
								<div class="th-inner">运费</div>
							</th>
						</tr>
					</thead>
					<tbody>
						{volist name="row.ordergoods" id="vo"}
						<tr>
							<td>{$vo.goods_sku_sn}</td>
							<td><a href="javascript:"><img class="img-sm img-center" src="{$vo.image|cdnurl|htmlentities}"></a></td>
							<td><strong>{$vo.title}</strong></td>
							<td>{$vo.difference}</td>
							<td>{$vo.number}</td>
							<td>{$vo.price}</td>
							<td>{$vo.price * $vo.number}</td>
							<td>{$vo.discount_price}</td>
							<td>{$vo.actual_payment}</td>
							<td>{$vo.freight_price}</td>
						</tr>
						{/volist}
					</tbody>
					<tfoot>
						<tr>
							<th colspan="10" style="text-align: right;">
								<span class="ordertext">商品总价：<samp class="text-red">￥{$row.pay.order_price}</samp></span>
								<span class="ordertext">运费（{switch name="row.freight_type"} 
								{case value="0"}运费叠加{/case} 
								{case value="1"}以最低结算{/case} 
								{case value="2"}以最高结算{/case} 
								{/switch}）：<samp class="text-red">￥{$row.pay.freight_price}</samp> </span>
								<span class="ordertext">{$row.discount_type}：<samp class="text-red">￥{$row.pay.discount_price}</samp> </span>
								<span class="ordertext">支付优惠：<samp class="text-red">￥{$row.pay.pay_discounts_price}</samp> </span>
								<span class="ordertext">实际支付：<samp class="text-red">￥{$row.pay.price}</samp></span>
							</th>
						</tr>
					</tfoot>
				</table>
				<div style="color: #333;">
					<p style="margin-bottom: 15px;">支付方式：
						<script>
							var obj=JSON.parse("{$row.pay.notice}");
							if(obj.type == 'balancens') {
								document.write('<span class="label label-info">永福莱豆</span>');
							}
						</script>
						{switch name="row.pay.pay_type"}
							{case value="0"}<span class="label label-info">{$row.pay_type_text|htmlentities}</span>{/case}
							{case value="1"}<span class="label label-warning">{$row.pay_type_text|htmlentities}</span>{/case}
						    {case value="2"}<span class="label label-primary">{$row.pay_type_text|htmlentities}</span>{/case}
							{case value="3"}<span class="label label-success">{$row.pay_type_text|htmlentities}</span>{/case}
							{case value="4"}<span class="label label-success">{$row.pay_type_text|htmlentities}</span>{/case}
							{case value="5"}<span class="label label-danger">{$row.pay_type_text|htmlentities}</span>{/case}
							{case value="6"}<span class="label label-warning">{$row.pay_type_text|htmlentities}</span>{/case}
						{/switch}
					</p>
					<p style="margin-bottom: 15px;">交易状态：
						{switch name="row.state"}
							{case value="0"}<span class="label label-info">{$row.state_text|htmlentities}</span>{/case}
							{case value="1"}<span class="label label-warning">{$row.state_text|htmlentities}</span>{/case}
						    {case value="2"}<span class="label label-primary">{$row.state_text|htmlentities}</span>{/case}
							{case value="3"}<span class="label label-success">{$row.state_text|htmlentities}</span>{/case}
							{case value="4"}<span class="label label-success">{$row.state_text|htmlentities}</span>{/case}
							{case value="5"}<span class="label label-danger">{$row.state_text|htmlentities}</span>{/case}
							{case value="6"}<span class="label label-warning">{$row.state_text|htmlentities}</span>{/case}
						{/switch}
					</p>
					{neq name="row.coupon_id" value="0"}
						<p>优惠折扣：{$row.coupon.name}
						</p>
					{/neq}
					<p>配送方式：<small>{$row.express_name ?: '未发货'}</small></p>
					{notempty name="row.remarks"}
						<p>买家留言：{$row.remarks}</small></p>
					{/notempty}
				</div>
			</div>
		</div>
	</div>
	
	{if condition="$row.state neq 7"}
	{if condition="$row.state egt 2"}
	<div class="col-md-12">
		<div class="panel panel-default">
			<div class="panel-heading">收货地址</div>
			<div class="panel-body">
				<table class="table table-striped">
					<tbody>
						<tr>
							<td>收货人</td>
							<td>{$row.address.name}</td>
						</tr>
						<tr>
							<td>联系电话</td>
							<td>{$row.address.mobile}</td>
						</tr>
						<tr>
							<td>收货地址</td>
							<td>{$row.address.address}-{$row.address.address_name}</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
	<div class="col-md-12">
		<div class="panel panel-default">
			<div class="panel-heading">付款信息</div>
			<div class="panel-body">
				<table class="table table-striped">
					<tbody>
						<tr>
							<td>交易号</td>
							<td>{$row.pay.pay_no}</td>
						</tr>
						<tr>
							<td>支付方式</td>
							<td>{$row.pay_type_text}</td>
						</tr>
						<tr>
							<td>交易订单号</td>
							<td>{$row.pay.trade_no}</td>
						</tr>
						<tr>
							<td>付款状态</td>
							<td>{$row.pay.pay_state_text}</td>
						</tr>
						<tr>
							<td>付款时间</td>
							<td>{$row.paymenttime_text}</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
	{/if}
	{if condition="$row.state egt 3"}
	<div class="col-md-12">
		<div class="panel panel-default">
			<div class="panel-heading">发货状态</div>
			<div class="panel-body">
				<table class="table table-bordered">
					<thead>
						<tr>
							<th class="text-center">
								<div class="th-inner">快递公司</div>
							</th>
							<th class="text-center">
								<div class="th-inner">快递号</div>
							</th>
							<th class="text-center">
								<div class="th-inner">发货状态</div>
							</th>
							<th class="text-center">
								<div class="th-inner">物流信息</div>
							</th>
							<th class="text-center">
								<div class="th-inner">更新时间</div>
							</th>
							<th class="text-center">
								<div class="th-inner">操作</div>
							</th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td>{$row.express_name}</td>
							<td>{$row.express_no}</td>
							<td>{$kuaidi.status}</td>
							<td>{$kuaidi.context}</td>
							<td>{$kuaidi.time}</td>
							<td><a href="javascript:;" class="btn btn-xs btn-info btn-selected kuaidisub" data-id="{$row.id}" data-toggle="tooltip" data-original-title="查看物流"><i class="fa fa-eye"></i></a></td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
	{/if}
	{/if}
</div>





<div class="hide layer-footer">
	<label class="control-label col-xs-12 col-sm-2"></label>
	<div class="col-xs-12 col-sm-8">
		<button type="reset" class="btn btn-primary btn-embossed btn-close" onclick="Layer.closeAll();">{:__('Close')}</button>
	</div>
</div>
