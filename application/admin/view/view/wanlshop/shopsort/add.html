<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
	<div class="form-group">
	    <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
	    <div class="col-xs-12 col-sm-8">
	        <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="">
	    </div>
	</div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Shop_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-shop_id" data-rule="required" 
			data-source="wanlshop/shop"
			data-field="shopname"
			class="form-control selectpage" 
			name="row[shop_id]" 
			type="text" value="">
        </div>
    </div>
    
	<div class="form-group">
	    <label for="pid" class="control-label col-xs-12 col-sm-2">{:__('Pid')}:</label>
	    <div class="col-xs-12 col-sm-8">
	        <select name="row[pid]" data-rule="required" id="pid" class="form-control">
	            <option value="0">{:__('None')}</option>
	            {foreach name="channelList" item="vo"}
	            <option value="{$vo.id}">{$vo.name}</option>
	            {/foreach}
	        </select>
	    </div>
	</div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-image" data-rule="required" class="form-control" size="50" name="row[image]" type="text" value="">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="plupload-image" class="btn btn-danger plupload" data-input-id="c-image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-image" class="btn btn-primary fachoose" data-input-id="c-image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-image"></span>
            </div>
            <ul class="row list-inline plupload-preview" id="p-image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Flag')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-flag" class="form-control selectpicker" multiple="" name="row[flag][]">
                {foreach name="flagList" item="vo"}
                    <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Isnav')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-isnav" data-rule="required" class="form-control" name="row[isnav]" type="number" value="1">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Weigh')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-weigh" data-rule="required" class="form-control" name="row[weigh]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="normal"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
