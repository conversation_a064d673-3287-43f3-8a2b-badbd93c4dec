<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        <ul class="nav nav-tabs" data-field="state">
            <li class="active"><a href="#t-all" data-value="" data-toggle="tab">{:__('All')}</a></li>
            {foreach name="stateList" item="vo"}
            <li><a href="#t-{$key}" data-value="{$key}" data-toggle="tab">{$vo}</a></li>
            {/foreach}
        </ul>
    </div>


    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                        <a href="javascript:;" class="btn btn-success btn-edit btn-disabled disabled {:$auth->check('wanlshop/shop/edit')?'':'hide'}" title="{:__('Edit')}" ><i class="fa fa-pencil"></i> {:__('Edit')}</a>
                        <a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled {:$auth->check('wanlshop/shop/del')?'':'hide'}" title="{:__('Delete')}" ><i class="fa fa-trash"></i> {:__('Delete')}</a>
                        <!--<div class="dropdown btn-group {:$auth->check('wanlshop/shop/multi')?'':'hide'}">-->
                        <!--    <a class="btn btn-primary btn-more dropdown-toggle btn-disabled disabled" data-toggle="dropdown"><i class="fa fa-cog"></i> {:__('More')}</a>-->
                        <!--    <ul class="dropdown-menu text-left" role="menu">-->
                        <!--        <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=normal"><i class="fa fa-eye"></i> {:__('Set to normal')}</a></li>-->
                        <!--        <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=hidden"><i class="fa fa-eye-slash"></i> {:__('Set to hidden')}</a></li>-->
                        <!--    </ul>-->
                        <!--</div>-->
                        <a href="wanlshop/goods" class="btn btn-info btn-dialog" data-area=["90%","80%"] title="{:__('全部商品')}" ><i class="fa fa-shopping-bag"></i> {:__('全部商品')}</a>
                        <a href="wanlshop/groups/goods" class="btn btn-warning btn-dialog" data-area=["90%","80%"] title="{:__('全部拼团')}" ><i class="fa fa-shopping-bag"></i> {:__('全部拼团')}</a>
						<a class="btn btn-success btn-recyclebin btn-dialog {:$auth->check('wanlshop/shop/recyclebin')?'':'hide'}" href="wanlshop/shop/recyclebin" title="{:__('Recycle bin')}"><i class="fa fa-recycle"></i> {:__('Recycle bin')}</a>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('wanlshop/shop/edit')}" 
                           data-operate-del="{:$auth->check('wanlshop/shop/del')}" 
                           width="100%">
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>