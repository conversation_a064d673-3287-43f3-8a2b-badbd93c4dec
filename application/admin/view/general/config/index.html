<style type="text/css">
    @media (max-width: 375px) {
        .edit-form tr td input {
            width: 100%;
        }

        .edit-form tr th:first-child, .edit-form tr td:first-child {
            width: 20%;
        }

        .edit-form tr th:nth-last-of-type(-n+2), .edit-form tr td:nth-last-of-type(-n+2) {
            display: none;
        }
    }

    .edit-form table > tbody > tr td a.btn-delcfg {
        visibility: hidden;
    }

    .edit-form table > tbody > tr:hover td a.btn-delcfg {
        visibility: visible;
    }

    @media (max-width: 767px) {
        .edit-form table tr th:nth-last-child(-n + 2), .edit-form table tr td:nth-last-child(-n + 2) {
            display: none;
        }
        .edit-form table tr td .msg-box {
            display: none;
        }
    }
</style>
<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        {:build_heading(null, false)}
        <ul class="nav nav-tabs">
            {foreach $siteList as $index=>$vo}
            <li class="{$vo.active?'active':''}"><a href="#{$vo.name}" data-toggle="tab">{:__($vo.title)}</a></li>
            {/foreach}
            <li data-toggle="tooltip" title="{:__('Add new config')}">
                <a href="#addcfg" data-toggle="tab"><i class="fa fa-plus"></i></a>
            </li>
        </ul>
    </div>

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <!--@formatter:off-->
            {foreach $siteList as $index=>$vo}
            <div class="tab-pane fade {$vo.active ? 'active in' : ''}" id="{$vo.name}">
                <div class="widget-body no-padding">
                    <form id="{$vo.name}-form" class="edit-form form-horizontal" role="form" data-toggle="validator" method="POST" action="{:url('general.config/edit')}">
                        {:token()}
                        <table class="table table-striped">
                            <thead>
                            <tr>
                                <th width="15%">{:__('Title')}</th>
                                <th width="68%">{:__('Value')}</th>
                                <th width="15%">{:__('Name')}</th>
                                <th width="2%"></th>
                            </tr>
                            </thead>
                            <tbody>
                            {foreach $vo.list as $item}
                            <tr>
                                <td>{$item.title}</td>
                                <td>
                                    <div class="row">
                                        <div class="col-sm-8 col-xs-12">
                                            {switch $item.type}
                                            {case string}
                                            <input {$item.extend_html} type="text" name="row[{$item.name}]" value="{$item.value|htmlentities}" class="form-control" data-rule="{$item.rule}" data-tip="{$item.tip}"/>
                                            {/case}
                                            {case text}
                                            <textarea {$item.extend_html} name="row[{$item.name}]" class="form-control" data-rule="{$item.rule}" rows="5" data-tip="{$item.tip}">{$item.value|htmlentities}</textarea>
                                            {/case}
                                            {case editor}
                                            <textarea {$item.extend_html} name="row[{$item.name}]" id="editor-{$item.name}" class="form-control editor" data-rule="{$item.rule}" rows="5" data-tip="{$item.tip}">{$item.value|htmlentities}</textarea>
                                            {/case}
                                            {case array}
                                            <dl {$item.extend_html} class="fieldlist" data-name="row[{$item.name}]">
                                                <dd>
                                                    <ins>{:isset($item["setting"]["key"])&&$item["setting"]["key"]?$item["setting"]["key"]:__('Array key')}</ins>
                                                    <ins>{:isset($item["setting"]["value"])&&$item["setting"]["value"]?$item["setting"]["value"]:__('Array value')}</ins>
                                                </dd>
                                                <dd><a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> {:__('Append')}</a></dd>
                                                <textarea name="row[{$item.name}]" class="form-control hide" cols="30" rows="5">{$item.value|htmlentities}</textarea>
                                            </dl>
                                            {/case}
                                            {case date}
                                            <input {$item.extend_html} type="text" name="row[{$item.name}]" value="{$item.value|htmlentities}" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-tip="{$item.tip}" data-rule="{$item.rule}"/>
                                            {/case}
                                            {case time}
                                            <input {$item.extend_html} type="text" name="row[{$item.name}]" value="{$item.value|htmlentities}" class="form-control datetimepicker" data-date-format="HH:mm:ss" data-tip="{$item.tip}" data-rule="{$item.rule}"/>
                                            {/case}
                                            {case datetime}
                                            <input {$item.extend_html} type="text" name="row[{$item.name}]" value="{$item.value|htmlentities}" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-tip="{$item.tip}" data-rule="{$item.rule}"/>
                                            {/case}
                                            {case datetimerange}
                                            <input {$item.extend_html} type="text" name="row[{$item.name}]" value="{$item.value|htmlentities}" class="form-control datetimerange" data-tip="{$item.tip}" data-rule="{$item.rule}"/>
                                            {/case}
                                            {case number}
                                            <input {$item.extend_html} type="number" name="row[{$item.name}]" value="{$item.value|htmlentities}" class="form-control" data-tip="{$item.tip}" data-rule="{$item.rule}"/>
                                            {/case}
                                            {case checkbox}
                                            <div class="checkbox">
                                            {foreach name="item.content" item="vo"}
                                            <label for="row[{$item.name}][]-{$key}"><input id="row[{$item.name}][]-{$key}" name="row[{$item.name}][]" type="checkbox" value="{$key}" data-tip="{$item.tip}" {in name="key" value="$item.value" }checked{/in} /> {$vo}</label>
                                            {/foreach}
                                            </div>
                                            {/case}
                                            {case radio}
                                            <div class="radio">
                                            {foreach name="item.content" item="vo"}
                                            <label for="row[{$item.name}]-{$key}"><input id="row[{$item.name}]-{$key}" name="row[{$item.name}]" type="radio" value="{$key}" data-tip="{$item.tip}" {in name="key" value="$item.value" }checked{/in} /> {$vo}</label>
                                            {/foreach}
                                            </div>
                                            {/case}
                                            {case value="select" break="0"}{/case}
                                            {case value="selects"}
                                            <select {$item.extend_html} name="row[{$item.name}]{$item.type=='selects'?'[]':''}" class="form-control selectpicker" data-tip="{$item.tip}" {$item.type=='selects'?'multiple':''}>
                                                {foreach name="item.content" item="vo"}
                                                <option value="{$key}" {in name="key" value="$item.value" }selected{/in}>{$vo}</option>
                                                {/foreach}
                                            </select>
                                            {/case}
                                            {case value="image" break="0"}{/case}
                                            {case value="images"}
                                            <div class="form-inline">
                                                <input id="c-{$item.name}" class="form-control" size="50" name="row[{$item.name}]" type="text" value="{$item.value|htmlentities}" data-tip="{$item.tip}">
                                                <span><button type="button" id="faupload-{$item.name}" class="btn btn-danger faupload" data-input-id="c-{$item.name}" data-mimetype="image/*" data-multiple="{$item.type=='image'?'false':'true'}" data-preview-id="p-{$item.name}"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                                                <span><button type="button" id="fachoose-{$item.name}" class="btn btn-primary fachoose" data-input-id="c-{$item.name}" data-mimetype="image/*" data-multiple="{$item.type=='image'?'false':'true'}"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                                                <span class="msg-box n-right" for="c-{$item.name}"></span>
                                                <ul class="row list-inline faupload-preview" id="p-{$item.name}"></ul>
                                            </div>
                                            {/case}
                                            {case value="file" break="0"}{/case}
                                            {case value="files"}
                                            <div class="form-inline">
                                                <input id="c-{$item.name}" class="form-control" size="50" name="row[{$item.name}]" type="text" value="{$item.value|htmlentities}" data-tip="{$item.tip}">
                                                <span><button type="button" id="faupload-{$item.name}" class="btn btn-danger faupload" data-input-id="c-{$item.name}" data-multiple="{$item.type=='file'?'false':'true'}"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                                                <span><button type="button" id="fachoose-{$item.name}" class="btn btn-primary fachoose" data-input-id="c-{$item.name}" data-multiple="{$item.type=='file'?'false':'true'}"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                                                <span class="msg-box n-right" for="c-{$item.name}"></span>
                                            </div>
                                            {/case}
                                            {case switch}
                                            <input id="c-{$item.name}" name="row[{$item.name}]" type="hidden" value="{:$item.value?1:0}">
                                            <a href="javascript:;" data-toggle="switcher" class="btn-switcher" data-input-id="c-{$item.name}" data-yes="1" data-no="0">
                                                <i class="fa fa-toggle-on text-success {if !$item.value}fa-flip-horizontal text-gray{/if} fa-2x"></i>
                                            </a>
                                            {/case}
                                            {case bool}
                                            <label for="row[{$item.name}]-yes"><input id="row[{$item.name}]-yes" name="row[{$item.name}]" type="radio" value="1" {$item.value?'checked':''} data-tip="{$item.tip}" /> {:__('Yes')}</label>
                                            <label for="row[{$item.name}]-no"><input id="row[{$item.name}]-no" name="row[{$item.name}]" type="radio" value="0" {$item.value?'':'checked'} data-tip="{$item.tip}" /> {:__('No')}</label>
                                            {/case}
                                            {case city}
                                            <div style="position:relative">
                                            <input {$item.extend_html} type="text" name="row[{$item.name}]" id="c-{$item.name}" value="{$item.value|htmlentities}" class="form-control" data-toggle="city-picker" data-tip="{$item.tip}" data-rule="{$item.rule}" />
                                            </div>
                                            {/case}
                                            {case value="selectpage" break="0"}{/case}
                                            {case value="selectpages"}
                                            <input {$item.extend_html} type="text" name="row[{$item.name}]" id="c-{$item.name}" value="{$item.value|htmlentities}" class="form-control selectpage" data-source="{:url('general.config/selectpage')}?id={$item.id}" data-primary-key="{$item.setting.primarykey}" data-field="{$item.setting.field}" data-multiple="{$item.type=='selectpage'?'false':'true'}" data-tip="{$item.tip}" data-rule="{$item.rule}" />
                                            {/case}
                                            {case custom}
                                            {$item.extend_html}
                                            {/case}
                                            {/switch}
                                        </div>
                                        <div class="col-sm-4"></div>
                                    </div>

                                </td>
                                <td>{php}echo "{\$site.". $item['name'] . "}";{/php}</td>
                                <td>{if $item['id']>17}<a href="javascript:;" class="btn-delcfg text-muted" data-name="{$item.name}"><i class="fa fa-times"></i></a>{/if}</td>
                            </tr>
                            {/foreach}
                            </tbody>
                            <tfoot>
                            <tr>
                                <td></td>
                                <td>
                                    <button type="submit" class="btn btn-success btn-embossed">{:__('OK')}</button>
                                    <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
                                </td>
                                <td></td>
                                <td></td>
                            </tr>
                            </tfoot>
                        </table>
                    </form>
                </div>
            </div>
            {/foreach}
            <div class="tab-pane fade" id="addcfg">
                <form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="{:url('general.config/add')}">
                    {:token()}
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-2">{:__('Group')}:</label>
                        <div class="col-xs-12 col-sm-4">
                            <select name="row[group]" class="form-control selectpicker">
                                {foreach name="groupList" item="vo"}
                                <option value="{$key}" {in name="key" value="basic" }selected{/in}>{$vo}</option>
                                {/foreach}
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-2">{:__('Type')}:</label>
                        <div class="col-xs-12 col-sm-4">
                            <select name="row[type]" id="c-type" class="form-control selectpicker">
                                {foreach name="typeList" item="vo"}
                                <option value="{$key}" {in name="key" value="string" }selected{/in}>{$vo}</option>
                                {/foreach}
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="name" class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
                        <div class="col-xs-12 col-sm-4">
                            <input type="text" class="form-control" id="name" name="row[name]" value="" data-rule="required; length(3~30); remote(general/config/check)"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="title" class="control-label col-xs-12 col-sm-2">{:__('Title')}:</label>
                        <div class="col-xs-12 col-sm-4">
                            <input type="text" class="form-control" id="title" name="row[title]" value="" data-rule="required"/>
                        </div>
                    </div>
                    <div class="form-group hidden tf tf-selectpage tf-selectpages">
                        <label for="c-selectpage-table" class="control-label col-xs-12 col-sm-2">{:__('Selectpage table')}:</label>
                        <div class="col-xs-12 col-sm-4">
                            <select id="c-selectpage-table" name="row[setting][table]" class="form-control selectpicker" data-live-search="true">
                                <option value="">{:__('Please select table')}</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group hidden tf tf-selectpage tf-selectpages">
                        <label for="c-selectpage-primarykey" class="control-label col-xs-12 col-sm-2">{:__('Selectpage primarykey')}:</label>
                        <div class="col-xs-12 col-sm-4">
                            <select name="row[setting][primarykey]" class="form-control selectpicker" id="c-selectpage-primarykey"></select>
                        </div>
                    </div>
                    <div class="form-group hidden tf tf-selectpage tf-selectpages">
                        <label for="c-selectpage-field" class="control-label col-xs-12 col-sm-2">{:__('Selectpage field')}:</label>
                        <div class="col-xs-12 col-sm-4">
                            <select name="row[setting][field]" class="form-control selectpicker" id="c-selectpage-field"></select>
                        </div>
                    </div>
                    <div class="form-group hidden tf tf-selectpage tf-selectpages">
                        <label class="control-label col-xs-12 col-sm-2">{:__('Selectpage conditions')}:</label>
                        <div class="col-xs-12 col-sm-8">
                            <dl class="fieldlist" data-name="row[setting][conditions]">
                                <dd>
                                    <ins>{:__('Field title')}</ins>
                                    <ins>{:__('Field value')}</ins>
                                </dd>

                                <dd><a href="javascript:;" class="append btn btn-sm btn-success"><i class="fa fa-plus"></i> {:__('Append')}</a></dd>
                                <textarea name="row[setting][conditions]" class="form-control hide" cols="30" rows="5"></textarea>
                            </dl>
                        </div>
                    </div>
                    <div class="form-group hidden tf tf-array">
                        <label for="c-array-key" class="control-label col-xs-12 col-sm-2">{:__('Array key')}:</label>
                        <div class="col-xs-12 col-sm-4">
                            <input type="text" name="row[setting][key]" class="form-control" id="c-array-key">
                        </div>
                    </div>
                    <div class="form-group hidden tf tf-array">
                        <label for="c-array-value" class="control-label col-xs-12 col-sm-2">{:__('Array value')}:</label>
                        <div class="col-xs-12 col-sm-4">
                            <input type="text" name="row[setting][value]" class="form-control" id="c-array-value">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="value" class="control-label col-xs-12 col-sm-2">{:__('Value')}:</label>
                        <div class="col-xs-12 col-sm-4">
                            <input type="text" class="form-control" id="value" name="row[value]" value="" data-rule=""/>
                        </div>
                    </div>
                    <div class="form-group hide" id="add-content-container">
                        <label for="content" class="control-label col-xs-12 col-sm-2">{:__('Content')}:</label>
                        <div class="col-xs-12 col-sm-4">
                            <textarea name="row[content]" id="content" cols="30" rows="5" class="form-control" data-rule="required(content)">value1|title1
value2|title2</textarea>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="tip" class="control-label col-xs-12 col-sm-2">{:__('Tip')}:</label>
                        <div class="col-xs-12 col-sm-4">
                            <input type="text" class="form-control" id="tip" name="row[tip]" value="" data-rule=""/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="rule" class="control-label col-xs-12 col-sm-2">{:__('Rule')}:</label>
                        <div class="col-xs-12 col-sm-4">
                            <div class="input-group pull-left">
                                <input type="text" class="form-control" id="rule" name="row[rule]" value="" data-tip="{:__('Rule tips')}"/>
                                <span class="input-group-btn">
                                    <button class="btn btn-primary dropdown-toggle" data-toggle="dropdown" type="button">{:__('Choose')}</button>
                                    <ul class="dropdown-menu pull-right rulelist">
                                        {volist name="ruleList" id="item"}
                                        <li><a href="javascript:;" data-value="{$key}">{$item}<span class="text-muted">({$key})</span></a></li>
                                        {/volist}
                                    </ul>
                                </span>
                            </div>
                            <span class="msg-box n-right" for="rule"></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="extend" class="control-label col-xs-12 col-sm-2">{:__('Extend')}:</label>
                        <div class="col-xs-12 col-sm-4">
                            <textarea name="row[extend]" id="extend" cols="30" rows="5" class="form-control" data-tip="{:__('Extend tips')}" data-rule="required(extend)" data-msg-extend="当类型为自定义时，扩展属性不能为空"></textarea>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-2"></label>
                        <div class="col-xs-12 col-sm-4">
                            <button type="submit" class="btn btn-success btn-embossed">{:__('OK')}</button>
                            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
                        </div>
                    </div>

                </form>

            </div>
            <!--@formatter:on-->
        </div>
    </div>
</div>
