<form id="change-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    {:token()}
    <input type="hidden" name="row[id]" value="{$row.id}">

    <div class="form-group">
        <label for="username" class="control-label col-xs-12 col-sm-2">{:__('Username')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="username" data-rule="required" class="form-control"  type="text" disabled="disabled" value="{$row.username|htmlentities}">
        </div>
    </div>

    <div class="form-group">
        <label for="nickname" class="control-label col-xs-12 col-sm-2">{:__('Nickname')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="nickname" data-rule="required" class="form-control"  type="text" disabled="disabled" value="{$row.nickname|htmlentities}">
        </div>
    </div>

    <div class="form-group">
        <label for="field" class="control-label col-xs-12 col-sm-2">资产类型:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_select('row[field]', $fields, null, ['id'=>'field','class'=>'form-control', 'required'=>''])}
        </div>
    </div>
    <div class="form-group">
        <label for="direction" class="control-label col-xs-12 col-sm-2">变更方向:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_select('row[direction]', $direction, null, ['id'=>'direction','class'=>'form-control',
            'required'=>''])}
        </div>
    </div>

    <div class="form-group">
        <label for="amount" class="control-label col-xs-12 col-sm-2">变更数量:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="number" class="form-control"  id="amount" name="row[amount]" autocomplete="off" value=""
                   data-rule="required;range(0~)"/>
        </div>
    </div>

    <div class="form-group">
        <label for="note" class="control-label col-xs-12 col-sm-2">备注:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control"  id="note" name="row[note]" autocomplete="off" value="" >
        </div>
    </div>
    <div class="form-group hidden">
        <label class="control-label col-xs-12 col-sm-2">定期释放:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
                <label for="state-0"><input id="state-1" name="row[state]" type="radio" value="1" v-model="state"> 是</label>
                <label for="state-1"><input id="state-0" name="row[state]" type="radio" value="0" v-model="state" checked> 否</label>
            </div>
        </div>
    </div>
    <div class="form-group hidden">
        <label for="note" class="control-label col-xs-12 col-sm-2">每月释放:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control"  id="release" name="row[release]" autocomplete="off" value="" >
        </div>
    </div>

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
