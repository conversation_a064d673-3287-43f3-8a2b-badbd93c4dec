<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Saas_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-saas_id" data-rule="required" data-source="saas/index" class="form-control selectpage" name="row[saas_id]" type="text" value="{$row.saas_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Activity_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-activity_id" data-rule="required" min="0" data-source="activity/index" class="form-control selectpage" name="row[activity_id]" type="text" value="{$row.activity_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Trade_no')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-trade_no" class="form-control" name="row[trade_no]" type="text" value="{$row.trade_no|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Order_no')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-order_no" class="form-control" name="row[order_no]" type="text" value="{$row.order_no|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Number')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-number" data-rule="required" min="0" class="form-control" name="row[number]" type="number" value="{$row.number|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Personnel_info')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-personnel_info" class="form-control" name="row[personnel_info]" type="text" value="{$row.personnel_info|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Check_in_info')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-check_in_info" class="form-control" name="row[check_in_info]" type="text" value="{$row.check_in_info|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Region')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-region" class="form-control" name="row[region]" type="text" value="{$row.region|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Mobile')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-mobile" class="form-control" name="row[mobile]" type="text" value="{$row.mobile|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_health')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-is_health" class="form-control selectpicker" name="row[is_health]">
                {foreach name="isHealthList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.is_health"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Health_info')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-health_info" class="form-control" name="row[health_info]" type="text" value="{$row.health_info|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Payment_type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-payment_type" data-rule="required" class="form-control selectpicker" name="row[payment_type]">
                {foreach name="paymentTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.payment_type"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Payment_notice')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-payment_notice" class="form-control" name="row[payment_notice]" type="text" value="{$row.payment_notice|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Payment_amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-payment_amount" data-rule="required" class="form-control" step="0.01" name="row[payment_amount]" type="number" value="{$row.payment_amount|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Payment_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-payment_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[payment_time]" type="text" value="{:$row.payment_time?datetime($row.payment_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Cancel_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-cancel_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[cancel_time]" type="text" value="{:$row.cancel_time?datetime($row.cancel_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Complete_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-complete_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[complete_time]" type="text" value="{:$row.complete_time?datetime($row.complete_time):''}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
