<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use think\Db;

/**
 * 店铺服务管理
 *
 * @icon fa fa-circle-o
 */
class ShopCode extends Backend
{

    /**
     * ShopCode模型对象
     * @var \app\common\model\ShopCode
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\common\model\ShopCode;
        $this->view->assign("statusList", $this->model->getStatusList());
        $this->view->assign("bindStatusList", $this->model->getBindStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                ->with(['shop'])
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','name','terminal_no','mark','createtime','updatetime','status','bind_status','bind_time']);
                $row->visible(['shop']);
                $row->getRelation('shop')->visible(['shopname']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }


    /**
     * 批量生成
     * @return void
     */
    public function batchadd(){
        $num = $this->request->param('num/d');
        if($num > 10000 || $num < 1){
            $this->error('批量生成，单次仅支持1~10000张');
        }
        Db::startTrans();
        try{
            $terNoPre = $this->model::getTerminalNoPre();
            $data = [];
            for($i = 1;$i<=$num;$i++){
                $data[] = ['terminal_no'=>$terNoPre.sprintf("%05d", $i)];
            }
            $this->model->saveAll($data);
            Db::commit();
        }catch(\Exception $e){
            Db::rollback();
            $this->error('生成异常'.$e->getMessage());
        }
        $this->success('操作成功');
    }

}
