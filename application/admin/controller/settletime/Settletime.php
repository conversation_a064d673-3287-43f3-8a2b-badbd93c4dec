<?php

namespace app\admin\controller\settletime;

use app\common\controller\Backend;
use app\common\library\Auth;
use think\Exception;

/**
 * 会员管理
 *
 * @icon fa fa-user
 */
class Settletime extends Backend
{

    protected $relationSearch = true;
    protected $searchFields = 'id';

    /**
     * @var \app\admin\model\User
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = model('Settletime');
    }
    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);
//            foreach ($list as $k => $v) {
//                $v->avatar = $v->avatar ? cdnurl($v->avatar, true) : letter_avatar($v->nickname);
//                $v->hidden(['password', 'salt', 'paypwd', 'psalt']);
//            }
            $result = array("total" => $list->total(), "rows" => $list->items());
            $listarr = collection($list->items())->toArray();
            if(!empty($listarr)){
                //提取所有字段名称
                $filed_arr = array_keys($listarr[0]);
                //处理数据
                $total = [];
                foreach ($filed_arr as $filed_key){
                    if($filed_key == 'invest_cash'
                        || $filed_key == 'invest_xnb'
                        || $filed_key == 'revenue'
                        || $filed_key == 'expense'
                        || $filed_key == 'salaray'
                    ) $total[$filed_key] = array_sum(array_column($listarr, $filed_key));//合计
                    else $total[$filed_key] = null;
                }
                //设置标题
                $total['id'] = '合计';
                //追加到头部
                array_unshift($result['rows'],$total);
//                array_unshift($result['rows'],$ratio);
                //追加到尾部
//                $result['rows'][] = $total;
//                $result['rows'][] = $ratio;
                $total = [];
                foreach ($filed_arr as $filed_key){
                    if($filed_key == 'invest_cash'
                        || $filed_key == 'invest_xnb'
                        || $filed_key == 'revenue'
                        || $filed_key == 'expense'
                        || $filed_key == 'salaray'
                    ) {
                        $total[$filed_key] = $this->model
                            ->where($where)
                            ->sum("{$filed_key}");//总计
                    }
                    else $total[$filed_key] = null;
                }
                //设置标题
                $total['id'] = '总计';
                //追加到头部
                array_unshift($result['rows'],$total);
            }
            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $this->token();
        }
        return parent::add();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        if ($this->request->isPost()) {
            $this->token();
        }
        $row = $this->model->get($ids);
        $this->modelValidate = true;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $this->view->assign('groupList', build_select('row[group_id]', \app\admin\model\UserGroup::column('id,name'), $row['group_id'], ['class' => 'form-control selectpicker']));
        $this->view->assign('levelList', build_select('row[vip_level]', \app\admin\model\UserVipLevel::column('id,name'), $row['vip_level'], ['class' => 'form-control selectpicker']));
        $this->view->assign('levelsList', build_select('row[vip_levels]', \app\admin\model\UserVipLevels::column('id,name'), $row['vip_levels'], ['class' => 'form-control selectpicker']));
        $this->view->assign('svipList', build_select('row[svip_level]', \app\admin\model\UserSvipLevel::column('id,name'), $row['svip_level'], ['class' => 'form-control selectpicker']));
        return parent::edit($ids);
    }

    /**
     * 删除
     */
    public function del($ids = "")
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ? $ids : $this->request->post("ids");
        $row = $this->model->get($ids);
        $this->modelValidate = true;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        Auth::instance()->delete($row['id']);
        $this->success();
    }

}
