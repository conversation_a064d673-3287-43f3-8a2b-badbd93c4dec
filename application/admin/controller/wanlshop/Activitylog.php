<?php

namespace app\admin\controller\wanlshop;

use app\common\controller\Backend;
use think\Db;
/**
 * 用户活动参与管理
 *
 * @icon fa fa-circle-o
 */
class Activitylog extends Backend
{
    
    /**
     * Activitylog模型对象
     * @var \app\admin\model\wanlshop\Activitylog
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\wanlshop\Activitylog;
        $kuaidi = new \app\index\model\wanlshop\Kuaidi;
        $this->view->assign("kuaidiList", $kuaidi->field('name,code')->select());

    }

    public function import()
    {
        parent::import();
    }

    public function edit($ids = null)
    {

        $findrow = $this->model->get($ids);
        if (!$findrow) {
            $this->error('No Results were found');
        }
        if ($this->request->isPost()) {

            $params = $this->request->post("row/a");
            if ($params) {
                
                $findrow->save($params);
                $this->success();
            }
            $this->error();
        }
        if($findrow['state']==0){
            $findrow['state'] = '未处理';
        }else if($findrow['state']==1){
            $findrow['state'] = '通过';
        }else if($findrow['state']==2){
            $findrow['state'] = '拒绝';
        }
        
        $this->view->assign("row", $findrow);

        return $this->view->fetch();
    }


    /**
     * 同意
     */
    public function agree($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }

        if ($this->request->isPost()) {
            if ($row['state'] > '0') {
                $this->error('请不要重复审核！');
            }
            $result = false;
            Db::startTrans();
            try {
                //是否采用模型验证
                $params = $this->request->post("row/a");
                // 审核通过
                $result = $row->allowField(true)->save([
                    'state' => 1,
                    'express_name' => $params['express_name'],
                    'express_no' => $params['express_no'],
                ]);
                Db::commit();
            } catch (ValidateException $e) {
                Db::rollback();
                $this->error($e->getMessage());
            } catch (PDOException $e) {
                Db::rollback();
                $this->error($e->getMessage());
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            if ($result !== false) {
                $this->success();
            } else {
                $this->error(__('No rows were updated'));
            }
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }


    /**
     * 拒绝
     */
    public function refuse($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                $result = false;
                Db::startTrans();
                try {
                    
                    $params['state'] = 2;
                    $result = $row->allowField(true)->save($params);
                    
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }





}
