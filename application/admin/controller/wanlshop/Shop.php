<?php

namespace app\admin\controller\wanlshop;

use app\common\controller\Backend;

/**
 * 店铺管理
 *
 * @icon fa fa-circle-o
 */
class Shop extends Backend
{
    
    /**
     * Shop模型对象
     * @var \app\admin\model\wanlshop\Shop
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\wanlshop\Shop;
        $this->view->assign("stateList", $this->model->getStateList());
        $this->view->assign("statusList", $this->model->getStatusList());
    }
    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax())
        {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField'))
            {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            if (SAAS > 0) {
                $where_saas = array(
                    'user.saas_id' => SAAS
                );
                $total = $this->model
                    ->with(['user'])
                    ->where($where)
                    ->where($where_saas)
                    ->order($sort, $order)
                    ->count();
            }else{
                $total = $this->model
                    ->with(['user'])
                    ->where($where)
                    ->order($sort, $order)
                    ->count();
            }

            if (SAAS > 0) {
                $where_saas = array(
                    'user.saas_id' => SAAS
                );
                $list = $this->model
                    ->with(['user'])
                    ->where($where)
                    ->where($where_saas)
                    ->order($sort, $order)
                    ->limit($offset, $limit)
                    ->select();
            }else{
                $list = $this->model
                    ->with(['user'])
                    ->where($where)
                    ->order($sort, $order)
                    ->limit($offset, $limit)
                    ->select();
            }
    
            foreach ($list as $row) {
                
                $row->getRelation('user')->visible(['username']);
            }
            $list = collection($list)->toArray();
            $result = array("total" => $total, "rows" => $list);
    
            return json($result);
        }
        return $this->view->fetch();
    }
}
