<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use PDOException;
use think\Cache;
use think\Db;
use think\Exception;
use think\exception\ValidateException;
use think\Hook;

use function fast\array_get;

/**
 * 支付商户
 *
 * @icon fa fa-circle-o
 */
class PayMerchant extends Backend
{

    protected $noNeedRight = ['getArea','getBank','getCategory','getLarIdType'];

    /**
     * PayMerchant模型对象
     * @var \app\common\model\PayMerchant
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\common\model\PayMerchant;
        $this->view->assign("isValidList", $this->model->getIsValidList());
        $this->view->assign("statusList", $this->model->getStatusList());
        $this->view->assign("channelList", $this->model->getChannelList());
        $this->view->assign("merchantStatusList", $this->model->getMerchantStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 业务锁
     * @param mixed $time 时间（秒）
     * @param mixed $processKey 密码锁
     * @return void
     */
    protected function serviceLock($time = 2,$processKey = ''){
        $redis = Cache::store('redis');

        !$processKey && $processKey = MD5($this->request->action().$this->auth->id);

        if($redis->has($processKey)){
            $this->error('请勿频繁操作');
        }
        $redis->set($processKey,1,$time);
    }

    /**
     * 主动卸载业务锁
     * @param mixed $processKey 密码锁
     * @return void
     */
    protected function unServiceLock($processKey = ''){
        $redis = Cache::store('redis');

        !$processKey && $processKey = MD5($this->request->action().$this->auth->id);

        $redis->rm($processKey);
    }


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['user','shop'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->hidden(['body','sub_info','term_info']);
                $row->getRelation('user')->visible(['username','nickname']);
				$row->getRelation('shop')->visible(['shopname']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }


    /**
     * 添加
     *
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {
        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }
        $result = false;
 
        $user_id = 0;

        if($params['shop_id']){
            $shop = model('app\api\model\wanlshop\Shop')->where('id',$params['shop_id'])->find();
        
            if(!$shop){
                $this->error('店铺信息异常');
            }

            if(model('\app\common\model\PayMerchant')->where(['shop_id'=>$shop['id'],'channel'=>$params['channel']])->find()){
                $this->error($shop['shopname'].'已在'.$params['channel'].'渠道入网，无需添加');
            }

            $user_id = $shop['user_id'];
        }

        $params = $this->setParams($params);

        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }
            $data = [
                'user_id'=>$user_id,
                'merchant_status'=>2,
            ] + $this->regMerchant($params,false);

            $result = $this->model->allowField(true)->save($data);
            $params['shop_id'] && $this->updateShop($shop,$params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }

    /**
     * 处理传递的参数
     * @param $params
     * @return mixed
     */
    protected function setParams($params)
    {
        $merNameLen = mb_strlen($params['merName']);

        if($merNameLen < 4 || $merNameLen > 20){
            $this->error('商户简称要求：4~20字；小微可使用格式推荐：商户_法人姓名');
        }
        $params['licenseName'] = $params['merRegName'];
        if(mb_strlen($params['merRegName']) > 20){
            $params['merRegName'] = str_replace(['(个体工商户)','（个体工商户）',' '],['','',''],$params['merRegName']);
            //替换后，还超长，继续处理
            if(mb_strlen($params['merRegName']) > 20){
                $params['merRegName'] = mb_substr($params['merRegName'],0,20);
            }
        }
        return $params;
    }


    /**
     * 编辑
     *
     * @param $ids
     * @return string
     * @throws DbException
     * @throws \think\Exception
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            $row['body'] = json_decode($row['body'],1);
            if(!$row['body']){
                $this->error('无法编辑','');
            }
            $this->view->assign('row', $row);
            $this->view->assign('edit', $this->request->param('edit',1));
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        if($row->channel_status && $row->channel_status != 'REJECT'){
            $this->error('当前状态，不允许编辑');
        }

        if($row->mer_no){
            $this->error('已入网，无法编辑信息，请使用变更信息');
        }

        $params = $this->setParams($params);

        $user_id = 0;
        if($params['shop_id']){
            $shop = model('app\api\model\wanlshop\Shop')->where('id',$params['shop_id'])->find();
        
            if(!$shop){
                $this->error('店铺信息异常');
            }

            if(model('\app\common\model\PayMerchant')->where(['shop_id'=>$shop['id'],'channel'=>$params['channel'],'id'=>['<>',$row->id]])->find()){
                $this->error($shop['shopname'].'已在'.$params['channel'].'渠道入网，不可关联');
            }

            $user_id = $shop['user_id'];
        }

        if(!$params['provinceName'] || !$params['cityName'] || !$params['countyName']){
            $this->error('请选择省市区信息');
        }

        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $data = [
                'user_id'=>$user_id,
                'merchant_status'=>2,
            ] + $this->regMerchant($params,false);
            $data['body'] = json_encode( json_decode($data['body'],1)+ json_decode($row['body'],1),JSON_UNESCAPED_UNICODE);
            $result = $row->allowField(true)->save($data);
            $params['shop_id'] && $this->updateShop($shop,$params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }


    /**
     * 编辑基本信息
     *
     * @param $ids
     * @return string
     * @throws DbException
     * @throws \think\Exception
     */
    public function editInfo($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        $settle = $this->request->param('settle');
        if (false === $this->request->isPost()) {
            $row['body'] = json_decode($row['body'],1);
            if(!$row['body']){
                $this->error('无法变更','');
            }
            $this->view->assign('row', $row);
            $this->view->assign('edit', $this->request->param('edit',1));
            if($settle){
                return $this->view->fetch('pay_merchant/edit_settle');
            }
            return $this->view->fetch();
        }
        if($row['channel_status'] != 'OPEN'){
            $this->error('当前状态，无法变更');
        }
        if($row['update_status'] == 'PASSING'){
            $this->error('变更信息审核中，待审核完成后操作');
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $action = 'updateSettle';
            if($settle != 1){
                $params = $this->setParams($params);
                $action = 'updateInfo';
                $data['reg_name'] = array_get($params,'merRegName',$row->reg_name);
                $data['name'] = array_get($params,'merName',$row->name);
                $data['larname'] = array_get($params,'larName',$row->larname);
                $data['mobile'] = array_get($params,'contactMobile',$row->mobile);
                //修改结算信息，不同步更新
                $row['shop_id'] && $this->updateShop(model('app\api\model\wanlshop\Shop')->where('id',$row['shop_id'])->find(),$params);
            }
            $this->channel = $row['channel'];
            $params['images'] && $params['attchments'] = $this->uploadAttchments($params['images'],'imgPath','imgType');

            $params['merchant_no'] = $row['merchant_no'];
            $body = json_decode($row['body'],1);
            //发送渠道编辑
            if($row['channel'] == 'LAKALA'){
                $req = [
                    'action'=>$action,
                    'type'=>2,
                    'data'=>$params,
                ];
                $res = Hook::listen('lakala',$req)[0];
                if(array_get($res,'reviewRelatedId')){
                    $data['update_id'] = $res['reviewRelatedId'];
                    $data['update_status'] = 'PASSING';//审核中
                }else{
                    $this->error($res['message']);
                }
            }elseif ($row['channel'] == 'HUIFU'){
                $req = [
                    'action'=>'regMerchant',
                    'data'=>$params + $body,
                ];
                $res = Hook::listen('huifu',$req)[0];

                if($res && array_get($res,'data') && array_get($res['data'],'huifu_id')){
                    $data['update_id'] = array_get($res['data'],'apply_no',$row->update_id);
                    $data['update_msg'] = $res['data']['resp_desc'];
                    if($res['data']['resp_code'] === '00000000'){
                         $data['update_status'] = 'PASS';//审核中
                    }else if($res['data']['resp_code'] === '90000000'){
                        $data['update_status'] = 'PASSING';//审核中
                    }else{
                        $data['update_status'] = 'UNPASS';//失败
                    }
                }else{
                    $this->error($res['data']['resp_desc'],'',$res['data']);
                }
            }

            //文件合并
            $params['images'] = $params['images'] + $body['images'];
            $params['attchments'] = $params['attchments'] + $body['attchments'];
            $data['body'] = json_encode($params + $body,JSON_UNESCAPED_UNICODE);
            $result = $row->allowField(true)->save($data);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success('','',$res);
    }

    /**
     * 更新商家相关信息
     */
    protected function updateShop($shop,$params){
        $state = ($params['merType'] == 'TP_PERSONAL') ? 0 : 1;
        $shop->shopname = $params['merName'];
        $shop->bio = $params['merName'];
        $shop->avatar = $params['images']['SHOP_OUTSIDE_IMG'];
        $shop->description = $params['merName'];
        $shop->address = $params['merAddr'];
        $shop->service_phone = $params['contactMobile'];
        $shop->images = $params['images']['SHOP_OUTSIDE_IMG'].','.$params['images']['SHOP_INSIDE_IMG'];
        isset($params['images']['BUSINESS_LICENCE']) && $shop->business_license = $params['images']['BUSINESS_LICENCE'];
        $shop->city = $params['provinceName'].'/'.$params['cityName'].'/'.$params['countyName'];
        $shop->city_id = model('app\common\model\Area')->where('name',$params['cityName'])->value('id') ?? 0;
        $lnglat = geocode(str_replace('/','', $shop->city).$shop->address);
        $shop->lng = $lnglat['data'][0];
        $shop->lat = $lnglat['data'][1];
        $shop->state = $state;
        return $shop->save();
    }

    /**
     * 驳回审核，重新填写
     * @return void
     */
    public function reject(){
        $id = $this->request->param('id');
        $reject_msg= $this->request->param('reject_msg');
        if(!$id){
            $this->error('缺少重要参数');
        }
        if(!$reject_msg){
            $this->error('请填写驳回原因');
        }
        $payMerchant = model('\app\common\model\PayMerchant')->get($id);
        if(!$payMerchant){
            $this->error('未知信息');
        }
        if($payMerchant->channel_status && $payMerchant->channel_status != 'REJECT'){
            $this->error('当前状态，不允许驳回');
        }
        if(($payMerchant['merchant_status'] != 2 && $payMerchant['merchant_status'] != 3 && $payMerchant['agreement_status'] != 2) || $payMerchant['mer_no']){
            $this->error('当前状态无法驳回操作');
        }
        try{
            $data['merchant_status'] = 1;
            //驳回需重新签订合同
            $data['contract_id'] = 0;
            $data['agreement_status'] = 1;
            $data['reject_msg'] = $reject_msg;
            $payMerchant->allowField(true)->save($data);
        }catch(PDOException|Exception $e){
            $this->error('提交异常'.$e->getMessage());
        }
        $this->success('驳回成功，待商户重新提交审核');
    }


    /**
     * 发起进件，注册商户
     * @return void
     */
    public function incoming(){
        $id = $this->request->param('id');
        if(!$id){
            $this->error('缺少重要参数');
        }
        $payMerchant = model('\app\common\model\PayMerchant')->get($id);
        if(!$payMerchant){
            $this->error('未知信息');
        }
        if($payMerchant['channel'] == 'LAKALA' && (!$payMerchant['contract_id'] || $payMerchant['agreement_status'] != 2)){
            $this->error('尚未签署入网协议');
        }
        if($payMerchant->channel_status && $payMerchant->channel_status != 'REJECT'){
            $this->error('当前状态，不允许操作');
        }
        if($payMerchant['mer_no']){
            $this->error('不可重复进件');
        }
        $body = json_decode($payMerchant['body'],1);
        
        $merNameLen = mb_strlen($body['merName']);
        
        if($merNameLen < 4 || $merNameLen > 20){
            $this->error('商户简称要求：4~20字；小微可使用格式推荐：商户_法人姓名');
        }
        $lock_key = $this->request->action().$id;
        $this->serviceLock(60,$lock_key);
        $extend = [];
        try{
            $payMerchant['contract_id'] && $extend = ['contractNo'=>model('\app\common\model\PayMerchantContract')->where('id',$payMerchant['contract_id'])->value('ec_no')];
            $extend['merchant_no'] = $payMerchant['merchant_no'];
            $extend['agreement_status'] = $payMerchant['agreement_status'];
            $extend['channel'] = $payMerchant['channel'];
            $data = $this->regMerchant($extend + $body,true);
            if($payMerchant['channel'] == 'LAKALA'){
                $data['wechat_auth'] = 2;
                $data['alipay_auth'] = 2;
            }
            $payMerchant->allowField(true)->save($data);
            //汇付进件之后需要开通业务,非拒绝时需要开通
            if(!$extend['merchant_no']){
                $this->openB($payMerchant);
            }
        }catch(PDOException|Exception $e){
            $this->error('提交异常'.$e->getMessage());
        } finally {
            $this->unServiceLock($lock_key);
        }
        $this->success();
    }

    /**
     * 开通业务
     * @return void
     */
    public function openBusi(){
        $id = $this->request->param('id');
        $contactMobile = $this->request->param('contactMobile');
        if(!$id){
            $this->error('缺少重要参数');
        }
        if(!$contactMobile){
            $this->error('请输入商户手机号');
        }
        $payMerchant = model('\app\common\model\PayMerchant')->get($id);
        if(!$payMerchant){
            $this->error('未知信息');
        }
        if($payMerchant['channel_status'] == 'REJECT'){
            $this->error('已被拒绝，无法开通业务');
        }
        if($payMerchant['update_status'] == 'PASSING'){
            $this->error('当前状态无法操作，请稍后重试');
        }
        $this->openB($payMerchant,['contactMobile'=>$contactMobile,'mobile'=>$contactMobile]);
    }

    /**
     * 开通业务
     * @param $payMerchant
     * @return void
     */
    protected function openB($payMerchant,$extend = []){
        if($payMerchant['channel'] == 'HUIFU' && $payMerchant->merchant_no && $payMerchant->channel_status != 'REJECT'){
            $req = [
                'action'=>'openBusi',
                'data'=>$extend + ['merchant_no'=>$payMerchant->merchant_no,'open_busi'=>$payMerchant->open_busi] + json_decode($payMerchant->body,1),
            ];
            $res = Hook::listen('huifu',$req)[0];
            if($res && array_get($res,'data') && array_get($res['data'],'apply_no')){
                if($res['data']['resp_code'] === '00000000'){
                    $data['update_id'] = $res['data']['apply_no'];
                    $data['update_status'] = 'PASS';//通过
                    $data['open_busi'] = '3';//1未开通，2待审核，3已开通
                }else if($res['data']['resp_code'] === '90000000'){
                    $data['update_id'] = $res['data']['apply_no'];
                    $data['update_status'] = 'PASSING';//待审核
                    $data['update_msg'] = '待商户短信签约';//待审核
                    $data['open_busi'] = '2';//1未开通，2待审核，3已开通
                }else{
                    $this->error('进件成功，开通业务异常：'.$res['data']['resp_desc'],'',$res['data']);
                }
                $payMerchant->allowField(true)->save($extend + $data);
            }else{
                $this->error($res['data']['resp_desc'],'',$res['data']);
            }
        }
        if($payMerchant->channel_status == 'REJECT'){
            $this->error($payMerchant->reject_msg);
        }
        $this->success('操作成功');
    }


    /**
     * 增终进件
     * @return void
     */
    public function addTerm(){
        $id = $this->request->param('id');
        $add_term = $this->request->param('add_term','WECHAT_PAY');//WECHAT_PAY : 专业化扫码
        if(!$id){
            $this->error('缺少重要参数');
        }
        $payMerchant = model('\app\common\model\PayMerchant')->get($id);
        if(!$payMerchant){
            $this->error('未知信息');
        }
        if($payMerchant['channel_status'] != 'OPEN'){
            $this->error('尚未开通，无法增终');
        }
        if($payMerchant['update_status'] == 'PASSING'){
            $this->error('变更信息审核中，待审核完成后操作');
        }
        if($payMerchant['add_term']){
            $this->error('不可重复增终进件');
        }
        try{
            $req = [
                'action'=>'addTerm',
                'type'=>2,
                'data'=>[
                    'mer_no'=>$payMerchant->mer_no,
                    'bzPos'=>$add_term,
                    'shop_no'=>$payMerchant->shop_no
                ],
            ];
            $res = Hook::listen('lakala',$req)[0];
            if($res['code'] == '000000'){
                $payMerchant->update_id = $res['data']['reviewRelatedId'];
                $payMerchant->update_status = 'PASSING';//审核中
                $payMerchant->add_term = $payMerchant->add_term ? $payMerchant->add_term.','.$add_term : $add_term;
                $payMerchant->save();
            }else{
                $this->error('增终异常'.$res['data']['message'],'',$res);
            }
            
        }catch(PDOException|Exception $e){
            $this->error('提交异常'.$e->getMessage());
        }
        $this->success();
    }
    protected $channel = 'LAKALA';//渠道：默认拉卡拉
    /**
     * 商家进件
     * @param mixed $params
     * @return array|array{body: bool|string, channel: string}
     */
    protected function regMerchant($params,$reg = true){
        $this->channel = $params['channel'];
        $data = [
            'channel'=>$params['channel'],
            'type'=>$params['merType'],
            'reg_name'=>$params['merRegName'],
            'name'=>$params['merName'],
            'larname'=>$params['larName'],
            'mobile'=>$params['contactMobile'],
        ];
        $params['address'] = $params['provinceName'].$params['cityName'].$params['countyName'].$params['merAddr'];
        //提交进件
        if($reg){
            $params['attchments'] = $this->uploadAttchments($params['images']);
            if($params['channel'] == 'LAKALA'){
                $code = geocode($params['address']);

                if($code['code'] == 0){
                    $params['latitude'] = $code['data'][0];
                    $params['longtude'] = $code['data'][1];
                }

                $req2 = [
                    'action'=>'regMerchant',
                    'type'=>2,
                    'data'=>$params,
                ];
                $res2 = Hook::listen('lakala',$req2)[0];

                if(array_get($res2,'merchantNo')){
                    $data['merchant_no'] = $res2['merchantNo'];
                    $data['channel_status'] = array_get($res2,'status','');
                    if($data['channel_status'] == 'WAIT_AUDI'){
                        $data['merchant_status'] = '3';//待审核
                    }
                }else{
                    $this->error($res2['message'],'',$res2);
                }
            }

            //汇付
            if($params['channel'] == 'HUIFU'){
                $req2 = [
                    'action'=>'regMerchant',
                    'data'=>$params,
                ];
                $res2 = Hook::listen('huifu',$req2)[0];

                if($res2 && array_get($res2,'data') && array_get($res2['data'],'huifu_id')){
                    $data['merchant_no'] = $res2['data']['huifu_id'];
                    if($res2['data']['resp_code'] === '00000000'){
                        $data['channel_status'] = 'OPEN';//开通
                        $data['merchant_status'] = '4';//通过
                    }else if($res2['data']['resp_code'] === '90000000'){
                        $data['channel_status'] = 'WAIT_AUDI';//待审核
                        $data['merchant_status'] = '3';//待审核
                    }else{
                        $data['reject_msg'] = $res2['data']['resp_desc'];
                        $data['channel_status'] = 'REJECT';//需要修改
                    }
                }else{
                    $this->error($res2['data']['resp_desc'],'',$res2['data']);
                }
            }
        }
        $data['body'] = json_encode($params,JSON_UNESCAPED_UNICODE);
        return $data;
    }


    /**
     * 上传附件
     * @param mixed $images
     * @return array{id: mixed, type: mixed[]}
     */
    protected function uploadAttchments($images,$imgPath = 'id',$imgType = 'type'){
        $attchments = [];
        $arr = [
            'ID_CARD_FRONT'=>'法人身份证（人像）',
            'ID_CARD_BEHIND'=>'法人身份证（国徽）',
            'SHOP_OUTSIDE_IMG'=>'门头照',
            'SHOP_INSIDE_IMG'=>'内景照',
            'BUSINESS_LICENCE'=>'营业执照',
            'OPENING_PERMIT'=>'开户许可证',
            'SETTLE_ID_CARD_FRONT'=>'结算人身份证（人像）',
            'SETTLE_ID_CARD_BEHIND'=>'结算人身份证（国徽）',
            'LETTER_OF_AUTHORIZATION'=>'授权协议',
        ];
        foreach($images as $key => &$value){
            if($value){
                //将附件上传
                if (!preg_match("/^http/", $value)) {
                    $pre = 'https://dmnuo.oss-cn-hangzhou.aliyuncs.com';
                    $value = $pre.$value;
                    $headers = get_headers($value);
                    if(stripos($headers[0], '200') == false){
                        $value = str_replace($pre,'https://'.$_SERVER['HTTP_HOST'],$value);
                    }
                }
                if($this->channel == 'LAKALA'){
                    $req = [
                        'action'=>'upload',
                        'type'=>2,
                        'data'=>[
                            'file'=>$value,
                            'imgType'=>$key,
                        ],
                    ];
                    $res = Hook::listen('lakala',$req)[0];
                    if($res && array_get($res,'status') &&  $res['status'] != '02'){
                        $attchments[] = [
                            $imgPath=>$res['url'],
                            $imgType=>$key,
                        ];
                    }else{
                        $this->error(array_get($arr,$key,$key).'：'.'附件上传异常-1','',$res);
                    }
                }
                if($this->channel == 'HUIFU'){
                    switch ($key){
                        case 'ID_CARD_FRONT'://正面
                            $file_type = 'F02';
                            break;
                        case 'ID_CARD_BEHIND'://背面
                            $file_type = 'F03';
                            break;
                        case 'SHOP_OUTSIDE_IMG'://门头
                            $file_type = 'F22';
                            break;
                        case 'SHOP_INSIDE_IMG'://内景
                            $file_type = 'F24';
                            break;
                        case 'BANK_CARD'://银行卡正面
                            $file_type = 'F13';
                            break;
                        case 'BUSINESS_LICENCE'://营业执照
                            $file_type = 'F07';
                            break;
                        case 'OPENING_PERMIT'://开户许可证
                            $file_type = 'F08';
                            break;
                        case 'SETTLE_ID_CARD_FRONT'://持卡人身份证正面
                            $file_type = 'F55';
                            break;
                        case 'SETTLE_ID_CARD_BEHIND'://持卡人身份证反面
                            $file_type = 'F56';
                            break;
                        case 'LETTER_OF_AUTHORIZATION'://授权协议
                            $file_type = 'F15';
                            break;
                        default:
                            $this->error('文件类型异常-2','',$key);
                    }

                    $req = [
                        'action'=>'upload',
                        'data'=>[
                            'file_type'=>$file_type,
                            'file_url'=>$value,
                        ],
                    ];
                    $res = Hook::listen('huifu',$req)[0];

                    if($res && array_get($res,'data') && array_get($res['data'],'resp_code') === '00000000'){
                        $attchments[$file_type] = $res['data']['file_id'];
                    }else{
                        $this->error(array_get($arr,$key,$key).'：'.$res['data']['resp_desc'],'',$res);
                    }
                }
            }
        }
        return $attchments;
    }



    /**
     * 获取地区信息
     * @return \think\response\Json
     */
    public function getArea(){
        $parentCode = $this->request->param('parentCode',1);
        $channel = $this->request->param('channel','LAKALA');
        if(!$channel || $channel == 'LAKALA'){
            $params = [
                'action'=>'area',
                'type'=>2,
                'data'=>[
                    'parentCode'=>$parentCode,
                    'isBank'=>$this->request->param('isBank',false) ? true : false,
                ],
            ];
            $list = Hook::listen('lakala',$params)[0];
            if($parentCode == 3300){
                array_unshift($list,[
                    'code'=>'3320',
                    'createTime'=>'',
                    'id'=>4174,
                    'name'=>'宁波市',
                    'optimistic'=>'宁波市',
                    'parentCode'=>'3300',
                    'updateTime'=>'',
                ]);
            }
            if($parentCode == 3320){
                $list = array_merge($list,[
                    [
                        'code'=>'3327',
                        'createTime'=>'',
                        'id'=>3327,
                        'name'=>'海曙区',
                        'optimistic'=>'海曙区',
                        'parentCode'=>'3320',
                        'updateTime'=>'',
                    ],
                    [
                        'code'=>'3328',
                        'createTime'=>'',
                        'id'=>3328,
                        'name'=>'北仑区',
                        'optimistic'=>'北仑区',
                        'parentCode'=>'3320',
                        'updateTime'=>'',
                    ],
                    [
                        'code'=>'3329',
                        'createTime'=>'',
                        'id'=>3329,
                        'name'=>'镇海区',
                        'optimistic'=>'镇海区',
                        'parentCode'=>'3320',
                        'updateTime'=>'',
                    ],
                    [
                        'code'=>'3330',
                        'createTime'=>'',
                        'id'=>3330,
                        'name'=>'江北区',
                        'optimistic'=>'江北区',
                        'parentCode'=>'3320',
                        'updateTime'=>'',
                    ],
                ]);
            }
        }else{
            $list = Db::name('area_'.strtolower($channel))->where('parentCode',$parentCode)->select();
        }
        $searchKey = $this->request->param('searchKey');
        $searchValue = $this->request->param('searchValue');
        if($searchValue){
            foreach($list as $info){
                if($info[$searchKey] == $searchValue){
                    $list = [$info];
                    break;
                }
            }
        }
        return json(['list' => $list, 'total' => count($list)]);
    }


    /**
     * 获取银行信息
     * @return \think\response\Json
     */
    public function getBank(){
        $channel = $this->request->param('channel','LAKALA');
        $areaCode = $this->request->param('areaCode');
        $bankName = $this->request->param('bankName');
        if(!$channel || $channel == 'LAKALA'){
            $params = [
                'action'=>'bank',
                'type'=>2,
                'data'=>[
                    'areaCode'=>$areaCode,
                    'bankName'=>$bankName,
                ],
            ];
            $list = Hook::listen('lakala',$params)[0];
        }else{
            $table = Db::name('union_bank_'.strtolower($channel));
            if($bankName){
                $table = $table->where('branchBankName|bankName','like','%'.$bankName.'%');
            }
            else if($areaCode){
                $areaName = str_replace('市','',Db::name('area_'.strtolower($channel))->where('code',$areaCode)->value('name'));
                $table = $table->whereOr('branchBankName','like','%'.$areaName.'%');
            }
            $list = $table->select();
        }
        $searchKey = $this->request->param('searchKey');
        $searchValue = $this->request->param('searchValue');
        if($searchValue){
            foreach($list as $info){
                if($info[$searchKey] == $searchValue){
                    $list = [$info];
                    break;
                }
            }
        }
        return json(['list' => $list, 'total' => count($list)]);
    }


    /**
     * 获取银行信息
     * @return \think\response\Json
     */
    public function getCategory(){
        $channel = $this->request->param('channel','LAKALA');
        $parentCode = $this->request->param('parentCode');
        if(!$channel || $channel == 'LAKALA'){
            $params = [
                'action'=>'category',
                'type'=>2,
                'data'=>[
                    'parentCode'=>$parentCode,
                ],
            ];
            $list = Hook::listen('lakala',$params)[0];
        }else{
            $table = Db::name('mcc_'.strtolower($channel));
            if($parentCode){
                $table = $table->where('mcc_name_A',$parentCode);
            }else{
                $table = $table->field('mcc_name_A as name,mcc_name_A code')->group('mcc_name_A');
            }
            $list = $table->select();
        }
        $searchKey = $this->request->param('searchKey');
        $searchValue = $this->request->param('searchValue');
        if($searchValue){
            foreach($list as $info){
                if($info[$searchKey] == $searchValue){
                    $list = [$info];
                    break;
                }
            }
        }
        return json(['list' => $list, 'total' => count($list)]);
    }


    /**
     * 获取对应渠道证件类型
     */
    public function getLarIdType(){
        $channel = $this->request->param('channel','LAKALA');
        if(!$channel || $channel == 'LAKALA'){
            $list = [
                ['id'=>'01','name'=>'身份证'],
                ['id'=>'02','name'=>'护照'],
                ['id'=>'03','name'=>'港澳通行证'],
                ['id'=>'04','name'=>'台胞证'],
                ['id'=>'10','name'=>'外国人永久居留身份证'],
                ['id'=>'11','name'=>'港澳居民居住证'],
                ['id'=>'12','name'=>'台湾居民居住证'],
            ];
        }else{
            $list = [
                ['id'=>'00','name'=>'身份证'],
                ['id'=>'01','name'=>'护照'],
                ['id'=>'11','name'=>'港澳台同胞通行证'],
                ['id'=>'14','name'=>'台胞证'],
                ['id'=>'13','name'=>'外国人永久居留身份证'],
                ['id'=>'15','name'=>'港澳台居住证'],
            ];
        }
        $searchKey = $this->request->param('searchKey');
        $searchValue = $this->request->param('searchValue');
        if($searchValue){
            foreach($list as $info){
                if($info[$searchKey] == $searchValue){
                    $list = [$info];
                    break;
                }
            }
        }
        return json(['list' => $list, 'total' => count($list)]);
    }


    /**
     * 复议提交
     */
    public function reconsider(){
        $id = $this->request->param('id');
        if(!$id){
            $this->error('缺少参数');
        }
        $row = $this->model->get($id);
        if(!$row){
            $this->error('未知数据');
        }
        if(!$row['contract_id'] || $row['agreement_status'] != 2){
            $this->error('尚未签署入网协议');
        }
        if($row['mer_no']){
            $this->error('无需提交复议');
        }
        if($row['mer_no'] || $row->channel_status != 'REJECT'){
            $this->error('无需提交复议');
        }
        $params = [
            'action'=>'reconsider',
            'type'=>2,
            'data'=>[
                'merchant_no'=>$row['merchant_no'],
            ],
        ];
        $res = Hook::listen('lakala',$params)[0];
        if($res['code'] == '000000'){
            $row->channel_status = 'WAIT_AUDI';//待审核
            $row->merchant_status = '3';//待审核
            $row->save();
            $this->success($res['message'],'',$res);
        }
        $this->error($res['message'],'',$res);
    }



    /**
     * 查询商户信息
     */
    public function queryMerchant(){
        $id = $this->request->param('id');
        if(!$id){
            $this->error('缺少参数');
        }
        $row = $this->model->get($id);
        if(!$row){
            $this->error('未知数据');
        }
        $params = [
            'action'=>'queryMerchant',
            'type'=>2,
            'data'=>[
                'merchant_no'=>$row['merchant_no'],
                'mer_no'=>$row['mer_no'],
            ],
        ];
        $res = Hook::listen('lakala',$params)[0];
        if($res['code'] == '000000'){
            if($row){
                $row->reg_name = $res['data']['customer']['merRegName'];
                $row->name = $res['data']['customer']['customerName'];
                !$row->larname && $row->larname = $res['data']['customer']['legalName'];
                !$row->mobile && $row->mobile = $res['data']['customer']['phoneNo'];
                $row->channel_status = $res['data']['customer']['customerStatus'];
                $row->is_valid = 2;
                if($row->channel_status == 'OPEN'){
                    $row->mer_no = $res['data']['customer']['externalCustomerNo'];
                    $row->shop_no = $res['data']['shopInfoList'][0]['shopId'];
                    $row->term_info = json_encode($res['data']['terminalInfo'],JSON_UNESCAPED_UNICODE);
                    $row->is_valid = 1;
                    $row->merchant_status = 4;//已完成
                }
                // $row->agreement_status = $res['data']['customer']['agreementStatus'] == 'TRUE' ? 2 : 1;
                if($row->channel_status == 'REJECT'){
                    //被拒，自动转为待审核，并记录被拒原因
                    $row->reject_msg = $res['data']['customer']['auditRemark'];
                    $row->merchant_status = 1;//待提交
                    //驳回需重新签订合同
//                    $row->contract_id = 0;
//                    $row->agreement_status = 1;
                    $row->save();
                    $this->error($row->reject_msg,'',$res);
                }
                $row->save();
            }
        }else{
            $this->error($res['message'],'',$res);
        }
        if($this->request->param('show') == 2){
            $this->success('','',$res);
        }
        $this->view->assign('row', $res['data']);
        return $this->view->fetch('merchant');
    }


    /**
     * 查询子商户信息
     */
    public function querySubMerchant(){
        $id = $this->request->param('id');
        if(!$id){
            $this->error('缺少参数');
        }
        $row = $this->model->get($id);
        if(!$row){
            $this->error('未知数据');
        }
        if ($row->channel == 'LAKALA'){
            $params = [
                'action'=>'queryMerchantNo',
                'type'=>2,
                'data'=>[
                    'mer_no'=>$row['mer_no'],
                ],
            ];
            $res = Hook::listen('lakala',$params)[0];
            if($res['code'] == '000000'){
                $sub_info = [
                    'wXList'=>[],
                    'zFBList'=>[],
                ];
                $row->sub_info && $sub_info = json_decode($row->sub_info,1) + $sub_info;
                foreach($res['data'] as $type => &$infos){
                    foreach($infos as &$v){
                        $v['auth'] = 2;//是否授权，1是，2否
                        foreach($sub_info[$type] as $vv){
                            if($v['channelId'] == $vv['channelId'] && $v['subMerchantNo'] == $vv['subMerchantNo']){
                                $v['auth'] = array_get($vv,'auth',2);
                            }
                            if($v['auth'] == 2){
                                $v['auth'] = $this->findAuth($type,$v['subMerchantNo'],$row->mer_no);
                            }
                            if($v['auth'] == 1){
                                if($type == 'wXList' && $v['channelId'] == '38630418'){
                                    $row->wechat_auth = 1;
                                }
                                if($type == 'zFBList'){
                                    $row->alipay_auth = 1;
                                }
                            }
                        }
                    }
                }
                $row->sub_info = json_encode($res['data'],JSON_UNESCAPED_UNICODE);
                $row->save();
            }else{
                $this->error($res['message'],'',$res);
            }
        }
        $this->view->assign('row', json_decode($row->sub_info,1));
        $this->view->assign('id', $id);
        $this->view->assign('channel', $row->channel);
        return $this->view->fetch('submerchant');
    }

    /**
     * 返回认证状态
     * @param $type
     * @param $subMerchantId
     * @param $merchantNo
     * @return int
     */
    private function findAuth($type,$subMerchantId,$merchantNo){
        $type == 'wXList' ? $tradeMode = 'WECHAT' : $tradeMode = 'ALIPAY';
        $params = [
            'action'=>'mrchAuthStateQuery',
            'data'=>[
                'tradeMode'=>$tradeMode,
                'subMerchantId'=>$subMerchantId,
                'merchantNo'=>$merchantNo,
            ],
        ];
        $res = Hook::listen('lakala',$params)[0];
        $auth = 2;
        if($res['retCode'] == '000000'){
            if(($tradeMode == 'WECHAT' && $res['respData']['checkResult'] == 'AUTHORIZE_STATE_AUTHORIZED') || ($tradeMode == 'ALIPAY' && $res['respData']['checkResult'] == 'AUTHORIZED')){
                $auth = 1;//已授权
            }
        }else{
            $this->error($res['retMsg'],'',$res);
        }
        return $auth;
    }

    /**
     * 查询子商户授权信息
     */
    public function querySubMerchantAuth(){
        $id = $this->request->param('id');
        if(!$id){
            $this->error('缺少参数');
        }
        $row = $this->model->get($id);
        if(!$row){
            $this->error('未知数据');
        }
        
        $sub_info = json_decode($row->sub_info,1) ?? [];

        foreach($sub_info as $type => &$infos){
            foreach($infos as &$v){
                if($v['auth'] == 1){
                    continue;
                }
                $v['auth'] = $this->findAuth($type,$v['subMerchantNo'],$row->mer_no);
                if($v['auth'] == 1){
                    if($type == 'wXList' && $v['channelId'] == '38630418'){
                        $row->wechat_auth = 1;
                    }
                    if($type == 'zFBList'){
                        $row->alipay_auth = 1;
                    }
                }
            }
        }
        $row->sub_info = json_encode($sub_info,JSON_UNESCAPED_UNICODE);
        $row->save();
        $this->success('同步成功');
    }

    /**
     * 查询变更状态
     */
    public function queryUpdate(){
        $id = $this->request->param('id');
        if(!$id){
            $this->error('缺少参数');
        }
        $row = $this->model->get($id);
        if(!$row){
            $this->error('未知数据');
        }
        if($row['update_status'] != 'PASSING'){
            $this->error('非待审核，无需查询');
        }
        if(!$row['update_id']){
            $this->error('缺少参数');
        }
        
        $params = [
            'action'=>'queryUpdate',
            'type'=>2,
            'data'=>[
                'update_id'=>$row['update_id'],
            ],
        ];
        $res = Hook::listen('lakala',$params)[0];
        if(array_get($res,'reviewPass')){
            $row->update_status = $res['reviewPass'];
            $row->update_msg = $res['reviewResult'] ?? '';
            $row->save();
        }else{
            $this->error('查询异常，请稍后再试','',$res);
        }
        $this->success($res['reviewResult'],'',$res);
    }

    /**
     * 申请合同
     */
    public function applyContract(){
        $id = $this->request->param('id');
        $again = $this->request->param('again');
        $ecTypeCode = $this->request->param('ecTypeCode','EC005');
        if(!$id){
            $this->error('缺少参数');
        }
        $row = $this->model->get($id);
        if(!$row){
            $this->error('未知数据');
        }
        $contractModel = model('\app\common\model\PayMerchantContract');
        $contract = $contractModel->get($row['contract_id']);
        //重新申请，或未申请过
        if(!$contract || $again){
            if($row['channel_status'] == 'OPEN'){
                $this->error('已开通无需申请合同','');
            }
            if($again && $row['agreement_status'] == 2){
                $this->error('入网协议已签署，无需重复申请合同','');
            }
            $params = [
                'action'=>'apply',
                'data'=>json_decode($row['body'],1)+['ecTypeCode'=>$ecTypeCode],
            ];
            $res = Hook::listen('lakala',$params)[0];
            if($res['retCode'] == '000000'){
                $contract = $contractModel;
                $contract->merchant_id = $id;
                $contract->mobile = $row['mobile'];
                $contract->type = $ecTypeCode;
                $contract->ec_no = '';
                $contract->ec_name = '';
                $contract->ec_status = '';
                $contract->order_no = $res['respData']['orderNo'];
                $contract->apply_id = $res['respData']['ecApplyId'];
                $contract->exp_time = $res['respData']['resultUrlExpTm'];
                $contract->result_url = $res['respData']['resultUrl'];
                $contract->notice = json_encode($res,JSON_UNESCAPED_UNICODE);
                $contract->save();
                $row->contract_id = $contract->id;
                $row->save();
            }else{
                $this->error($res['retMsg'],'',$res);
            }
        }
        if($again){
            $this->success('','',json_decode($contract->notice,1));
        }else{
            $this->view->assign('row', $row);
            $this->view->assign('contract', $contract);
            return $this->view->fetch('contract');
        }
    }


    /**
     * 账户余额
     */
    public function balanceQuery(){
        $id = $this->request->param('id');
        if(!$id){
            $this->error('缺少参数');
        }
        $row = $this->model->get($id);
        if(!$row){
            $this->error('未知数据');
        }
        $params = [
            'action'=>'balanceQuery',
            'data'=>[
                'mer_no'=>$row['mer_no']
            ],
        ];
        $balance = Hook::listen('lakala',$params)[0];
        if($balance['retCode'] == '000000'){
            $this->view->assign('row', $row);
            $this->view->assign('balance', $balance['respData']);
            return $this->view->fetch('balance');
        }else{
            $this->error($balance['retMsg'],'',$balance);
        }
    }


    /**
     * 微信商户配置
     * @return void
     */
    public function wechatConfig(){
        $id = $this->request->param('id');
        if(!$id){
            $this->error('缺少参数');
        }
        $row = $this->model->get($id);
        if(!$row){
            $this->error('未知数据');
        }
        $config = get_addon_config('wanlshop');
        $wx_appid = $config['mp_weixin']['appid'];
        $gz_appid = $config['sdk_qq']['gz_appid'];
        $params = [
            'action'=>'wechatConfig',
            'data'=>[
//                'wx_applet_app_id'=>$wx_appid,
                'wx_woa_app_id'=>$gz_appid,
                'huifu_id'=>$row->merchant_no,
            ]
        ];
        $res = Hook::listen('huifu',$params)[0]['data'];
        if($res['resp_code'] == '00000000'){
            $row->wx_appid = $wx_appid.','.$gz_appid;
            $row->save();
            $this->success($res['resp_desc'],'',$res);
        }
        $this->error($res['resp_desc'],'',$res);
    }

    /**
     * 提交微信实名认证
     * @return void
     */
    public function wechatAuth(){
        $id = $this->request->param('id');
        if(!$id){
            $this->error('缺少参数');
        }
        $row = $this->model->get($id);
        if(!$row){
            $this->error('未知数据');
        }
        //非未申请
        if($row->wechat_auth != 3){
            $this->error('请勿重复申请');
        }
        $data = json_decode($row['body'],1);
        $larIdAddr = array_get($data,'larIdAddr',$data['address']);
        $params = [
            'action'=>'wechatAuth',
            'data'=>[
                'huifu_id'=>$row->merchant_no,
                'owner'=>'Y',//经营者/法人是否为受益人Y：是最终受益人；N：非最终受益人
                'identification_address'=>$larIdAddr ? $larIdAddr : $data['address'],//法人证件居住地址
                'name'=>$data['larName'],//联系人姓名
                'mobile'=>$data['contactMobile'],//联系人手机号
                'id_card_number'=>$data['larIdCard'],//联系人身份证号码
                'contact_type'=>'LEGAL',//LEGAL：经营者/法人；SUPER：经办人
                'cert_type'=>'CERTIFICATE_TYPE_2389',//证书类型 CERTIFICATE_TYPE_2389：统一社会信用代码证书 ；
                'cert_number'=>$data['licenseNo'],//证书编号
                'cert_copy'=>array_get($data['attchments'],'F07'),//证书照片
                /**
                 * 小微经营类型
                 * 门店场所：MICRO_TYPE_STORE
                 * 流动经营/便民服务：MICRO_TYPE_MOBILE
                 * 线上商品/服务交易：MICRO_TYPE_ONLINE
                 */
                'micro_biz_type'=>'MICRO_TYPE_STORE',
                'store_name'=>$data['merName'],
                'store_header_copy'=>array_get($data['attchments'],'F22'),
                'store_indoor_copy'=>array_get($data['attchments'],'F24'),
                'store_address_code'=>$data['cityCode'],
                'store_address'=>$data['address'],
                'identification_front_copy'=>array_get($data['attchments'],'F02'),
                'identification_back_copy'=>array_get($data['attchments'],'F03'),
            ]
        ];
        $res = Hook::listen('huifu',$params)[0]['data'];
        if($res['resp_code'] == '00000000'){
            $row->wechat_auth = 2;//待认证
            $row->save();
            $this->success($res['resp_desc'],'',$res);
        }
        $this->error($res['resp_desc'],'',$res);
    }

    /**
     * 查询微信认证状态
     * @return void
     */
    public function queryWechatAuth(){
        $id = $this->request->param('id');
        if(!$id){
            $this->error('缺少参数');
        }
        $row = $this->model->get($id);
        if(!$row){
            $this->error('未知数据');
        }
        $params = [
            'action'=>'queryWechatAuth',
            'data'=>[
                'huifu_id'=>$row->merchant_no,
            ]
        ];
        $res = Hook::listen('huifu',$params)[0]['data'];
        if($res['resp_code'] == '00000000'){
            if($res['authorize_stat'] == 1){
                $row->wechat_auth = 1;//已认证
            }
            $qrcode_data = array_get($res,'qrcode_data','');
            $qrcode = '';
            $qrcode_data && $qrcode = 'data:image/png;base64,'.$qrcode_data;
            $row->sub_info = json_encode([
                    'wXList'=>[
                        [
                            'channelId'=>$res['pay_channel_id'],
                            'registerChannel'=>'',
                            'registerChannelName'=>'',
                            'registerType'=>'微信',
                            'subMerchantNo'=>$res['smid'],
                            'type'=>'',
                            'qrcode'=>$qrcode,
                            'auth'=>$row->wechat_auth,
                        ]
                    ]
                ] + json_decode($row->sub_info ?? '{}',1),JSON_UNESCAPED_UNICODE);
            $row->save();
            if(in_array($res['applyment_stat'],['APPLYMENT_STATE_REJECTED','APPLYMENT_STATE_CANCELED'])){
                $this->error($res['reject_reason'],'',$res);
            }
            $this->success($res['resp_desc'],'',$res);
        }
        $this->error($res['resp_desc'],'',$res);
    }


    /**
     * 提交支付宝实名认证
     * @return void
     */
    public function alipayAuth(){
        $id = $this->request->param('id');
        if(!$id){
            $this->error('缺少参数');
        }
        $row = $this->model->get($id);
        if(!$row){
            $this->error('未知数据');
        }
        //非未申请
        if($row->alipay_auth != 3){
            $this->error('请勿重复申请');
        }
        $data = json_decode($row['body'],1);
        $ent_type = 0;
        if($data['merType'] == 'TP_MERCHANT'){
            //企业进件
            $ent_type = 5;//个体工商户
            if(strstr($data['merRegName'],'公司')){
                $ent_type = 3;//私营企业
            }
            //非小微
            $extend = [
                'certificate_info'=>[
                    /**
                     * 登记证书类型
                     * 证照类型为登记证书时(certificate_type=REGISTER_CERT)必填。枚举：
                     * 统一社会信用代码证书(CERTIFICATE_TYPE_2389)
                     * 慈善组织公开募捐资格证书(CERTIFICATE_TYPE_2397)
                     * 社会团体法人登记证书(CERTIFICATE_TYPE_2394)
                     * 民办非企业单位登记证书(CERTIFICATE_TYPE_2395)
                     * 基金会法人登记证书(CERTIFICATE_TYPE_2396)
                     * 农民专业合作社法人营业执照(CERTIFICATE_TYPE_2398)
                     * 宗教活动场所登记证(CERTIFICATE_TYPE_2399)
                     * 其他证书/批文/证明(CERTIFICATE_TYPE_2400)
                     * 示例值：CERTIFICATE_TYPE_2389
                     */
                    'cert_type'=>'CERTIFICATE_TYPE_2389',
                    'cert_number'=>$data['licenseNo'],//证照编号
                    'cert_copy'=>array_get($data['attchments'],'F07'),//	证照图片
                    'cert_merchant_name'=>$data['merRegName'],//证照商户名称
                    'cert_legal_person'=>$data['larName'],//证照法人姓名
                    'cert_company_address'=>$data['address'],//证照注册地址
                    'effect_time'=>str_replace('-','',$data['larIdCardStart']),//证照生效时间
                    'expire_time'=>str_replace('-','',$data['larIdCardEnd']),//证照过期时间
                ],//登记证书信息
            ];
        }else{
            //小微
            $extend = [
                'support_credentials'=>[
                    /**
                     * 小微经营类型
                     * 门店场所：MICRO_TYPE_STORE
                     * 流动经营/便民服务：MICRO_TYPE_MOBILE
                     */
                    'micro_biz_type'=>'MICRO_TYPE_STORE',
                    'store_name'=>$data['merName'],//门店名称
                    'province_code'=>$data['provinceCode'],//门店省市编码
                    'province'=>$data['provinceName'],//	门店省份
                    'city_code'=>$data['cityCode'],//	门店市行政区号
                    'city'=>$data['cityName'],//	门店城市
                    'district_code'=>$data['countyCode'],//门店街道区号
                    'district'=>$data['countyName'],//	门店街道
                    'store_address'=>$data['address'],//门店场所填写门店详细地址
                    'store_door_img'=>array_get($data['attchments'],'F22'),//门店门头照信息或摊位照
                    'store_inner_img'=>array_get($data['attchments'],'F24'),//门店店内照片或者摊位照侧面
                ],//	辅助证明材料信息
            ];
        }
        $params = [
            'action'=>'alipayAuth',
            'data'=>[
                'huifu_id'=>$row->merchant_no,
                'auth_identity_info'=>json_encode([
                    /**
                     * 0 - 个人/小微
                     * 1 - 政府机构
                     * 2 - 国营企业
                     * 3 - 私营企业
                     * 4 - 外资企业
                     * 5 - 个体工商户
                     * 7 - 事业单位
                     */
                    'business_type'=>$ent_type,//主体类型
                    'certificate_type'=>'BUSINESS_CERT',//证照类型:营业执照(BUSINESS_CERT)；登记证书(REGISTER_CERT)；
                ]+$extend,JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES),
                'contact_person_info'=>json_encode([
                    'name'=>$data['larName'],//联系人姓名
                    'mobile'=>$data['contactMobile'],//联系人手机号
                    'id_card_number'=>$data['larIdCard'],//联系人身份证号码
                ],JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES),
            ]
        ];
        $res = Hook::listen('huifu',$params)[0]['data'];
        if($res['resp_code'] == '00000000'){
            $row->alipay_auth = 2;//待认证
            $row->save();
            $this->success($res['resp_desc'],'',$res);
        }
        $this->error($res['resp_desc'],'',$res);
    }

    /**
     * 查询支付宝认证状态
     * @return void
     */
    public function queryAlipayAuth(){
        $id = $this->request->param('id');
        if(!$id){
            $this->error('缺少参数');
        }
        $row = $this->model->get($id);
        if(!$row){
            $this->error('未知数据');
        }
        $params = [
            'action'=>'queryAlipayAuth',
            'data'=>[
                'huifu_id'=>$row->merchant_no,
            ]
        ];
        $res = Hook::listen('huifu',$params)[0]['data'];
        if($res['resp_code'] == '00000000'){
            if($res['authorize_stat'] == 'AUTHORIZED'){
                $row->alipay_auth = 1;//已认证
            }
            $row->sub_info = json_encode([
                    'zFBList'=>[
                        [
                            'channelId'=>'',
                            'registerChannel'=>'',
                            'registerChannelName'=>'',
                            'registerType'=>'支付宝',
                            'subMerchantNo'=>array_get($res,'smid'),
                            'type'=>'',
                            'qrcode'=>array_get($res,'qrcode',''),
                            'auth'=>$row->alipay_auth,
                        ]
                    ]
                ] + json_decode($row->sub_info ?? '{}',1),JSON_UNESCAPED_UNICODE);
            $row->save();
            $this->success($res['resp_desc'],'',$res);
        }
        $this->error($res['resp_desc'],'',$res);
    }
}
