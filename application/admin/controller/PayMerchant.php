<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use PDOException;
use think\Cache;
use think\Db;
use think\Exception;
use think\exception\ValidateException;
use think\Hook;

use function fast\array_get;

/**
 * 支付商户
 *
 * @icon fa fa-circle-o
 */
class PayMerchant extends Backend
{

    /**
     * PayMerchant模型对象
     * @var \app\common\model\PayMerchant
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\common\model\PayMerchant;
        $this->view->assign("isValidList", $this->model->getIsValidList());
        $this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 业务锁
     * @param mixed $time 时间（秒）
     * @param mixed $processKey 密码锁
     * @return void
     */
    protected function serviceLock($time = 2,$processKey = ''){
        $redis = Cache::store('redis');

        !$processKey && $processKey = MD5($this->request->action().$this->auth->id);

        if($redis->has($processKey)){
            $this->error('请勿频繁操作');
        }
        $redis->set($processKey,1,$time);
    }

    /**
     * 主动卸载业务锁
     * @param mixed $processKey 密码锁
     * @return void
     */
    protected function unServiceLock($processKey = ''){
        $redis = Cache::store('redis');

        !$processKey && $processKey = MD5($this->request->action().$this->auth->id);

        $redis->rm($processKey);
    }


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                ->with(['user','shop'])
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            foreach ($list as $row) {
                $row->hidden(['body','sub_info','term_info']);
                $row->getRelation('user')->visible(['username','nickname']);
                $row->getRelation('shop')->visible(['shopname']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }


    /**
     * 添加
     *
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {
        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }
        $result = false;

        $user_id = 0;

        if($params['shop_id']){
            $shop = model('app\api\model\wanlshop\Shop')->where('id',$params['shop_id'])->find();

            if(!$shop){
                $this->error('店铺信息异常');
            }

            if(model('\app\common\model\PayMerchant')->where(['shop_id'=>$shop['id'],'channel'=>'LAKALA'])->find()){
                $this->error($shop['shopname'].'已存在入网信息，无需添加');
            }

            $user_id = $shop['user_id'];
        }

        $params = $this->setParams($params);

        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }
            $data = [
                    'user_id'=>$user_id,
                    'merchant_status'=>2,
                ] + $this->regMerchant($params,false);

            $result = $this->model->allowField(true)->save($data);
            $params['shop_id'] && $this->updateShop($shop,$params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }

    /**
     * 处理传递的参数
     * @param $params
     * @return mixed
     */
    protected function setParams($params)
    {
        $merNameLen = mb_strlen($params['merName']);

        if($merNameLen < 4 || $merNameLen > 20){
            $this->error('商户简称要求：4~20字；小微可使用格式推荐：商户_法人姓名');
        }
        if(mb_strlen($params['merRegName']) > 20){
            $params['licenseName'] = $params['merRegName'];
            $params['merRegName'] = str_replace(['(个体工商户)','（个体工商户）',' '],['','',''],$params['merRegName']);
            //替换后，还超长，继续处理
            if(mb_strlen($params['merRegName']) > 20){
                $params['merRegName'] = mb_substr($params['merRegName'],0,20);
            }
        }
        return $params;
    }


    /**
     * 编辑
     *
     * @param $ids
     * @return string
     * @throws DbException
     * @throws \think\Exception
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            $row['body'] = json_decode($row['body'],1);
            if(!$row['body']){
                $this->error('无法编辑','');
            }
            $this->view->assign('row', $row);
            $this->view->assign('edit', $this->request->param('edit',1));
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        if($row->mer_no){
            $this->error('已入网，无法编辑信息，请使用变更信息');
        }

        $params = $this->setParams($params);

        $user_id = 0;
        if($params['shop_id']){
            $shop = model('app\api\model\wanlshop\Shop')->where('id',$params['shop_id'])->find();

            if(!$shop){
                $this->error('店铺信息异常');
            }

            if(model('\app\common\model\PayMerchant')->where(['shop_id'=>$shop['id'],'channel'=>'LAKALA','id'=>['<>',$row->id]])->find()){
                $this->error($shop['shopname'].'已存在入网信息，不可关联');
            }

            $user_id = $shop['user_id'];
        }

        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $data = [
                    'user_id'=>$user_id,
                    'merchant_status'=>2,
                ] + $this->regMerchant($params,false);
            $data['body'] = json_encode( json_decode($data['body'],1)+ json_decode($row['body'],1),JSON_UNESCAPED_UNICODE);
            $result = $row->allowField(true)->save($data);
            $params['shop_id'] && $this->updateShop($shop,$params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }


    /**
     * 编辑基本信息
     *
     * @param $ids
     * @return string
     * @throws DbException
     * @throws \think\Exception
     */
    public function editInfo($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        $settle = $this->request->param('settle');
        if (false === $this->request->isPost()) {
            $row['body'] = json_decode($row['body'],1);
            if(!$row['body']){
                $this->error('无法变更','');
            }
            $this->view->assign('row', $row);
            $this->view->assign('edit', $this->request->param('edit',1));
            if($settle){
                return $this->view->fetch('pay_merchant/edit_settle');
            }
            return $this->view->fetch();
        }
        if($row['channel_status'] != 'OPEN'){
            $this->error('当前状态，无法变更');
        }
        if($row['update_status'] == 'PASSING'){
            $this->error('变更信息审核中，待审核完成后操作');
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $action = 'updateSettle';
            if($settle != 1){
                $params = $this->setParams($params);
                $action = 'updateInfo';
                $data['reg_name'] = array_get($params,'merRegName',$row->reg_name);
                $data['name'] = array_get($params,'merName',$row->name);
                $data['larname'] = array_get($params,'larName',$row->larname);
                $data['mobile'] = array_get($params,'contactMobile',$row->mobile);
                //修改结算信息，不同步更新
                $row['shop_id'] && $this->updateShop(model('app\api\model\wanlshop\Shop')->where('id',$row['shop_id'])->find(),$params);
            }
            $params['images'] && $params['attchments'] = $this->uploadAttchments($params['images'],'imgPath','imgType');
            $data = [
                'body'=>json_encode($params + json_decode($row['body'],1),JSON_UNESCAPED_UNICODE)
            ];
            $params['merchant_no'] = $row['merchant_no'];
            //发送渠道编辑
            $req = [
                'action'=>$action,
                'type'=>2,
                'data'=>$params,
            ];
            $res = Hook::listen('lakala',$req)[0];
            if(array_get($res,'reviewRelatedId')){
                $data['update_id'] = $res['reviewRelatedId'];
                $data['update_status'] = 'PASSING';//审核中
            }else{
                $this->error($res['message']);
            }
            $result = $row->allowField(true)->save($data);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success('','',$res);
    }

    /**
     * 更新商家相关信息
     */
    protected function updateShop($shop,$params){
        $state = ($params['merType'] == 'TP_PERSONAL') ? 0 : 1;
        $shop->shopname = $params['merName'];
        $shop->bio = $params['merName'];
        $shop->avatar = $params['images']['SHOP_OUTSIDE_IMG'];
        $shop->description = $params['merName'];
        $shop->address = $params['merAddr'];
        $shop->service_phone = $params['contactMobile'];
        $shop->images = $params['images']['SHOP_OUTSIDE_IMG'].','.$params['images']['SHOP_INSIDE_IMG'];
        isset($params['images']['BUSINESS_LICENCE']) && $shop->business_license = $params['images']['BUSINESS_LICENCE'];
        $shop->city = $params['provinceName'].'/'.$params['cityName'].'/'.$params['countyName'];
        $shop->city_id = model('app\common\model\Area')->where('name',$params['cityName'])->value('id') ?? 0;
        $lnglat = geocode(str_replace('/','', $shop->city).$shop->address);
        $shop->lng = $lnglat['data'][0];
        $shop->lat = $lnglat['data'][1];
        $shop->state = $state;
        return $shop->save();
    }

    /**
     * 驳回审核，重新填写
     * @return void
     */
    public function reject(){
        $id = $this->request->param('id');
        $reject_msg= $this->request->param('reject_msg');
        if(!$id){
            $this->error('缺少重要参数');
        }
        if(!$reject_msg){
            $this->error('请填写驳回原因');
        }
        $payMerchant = model('\app\common\model\PayMerchant')->get($id);
        if(!$payMerchant){
            $this->error('未知信息');
        }
        if(($payMerchant['merchant_status'] != 2 && $payMerchant['merchant_status'] != 3 && $payMerchant['agreement_status'] != 2) || $payMerchant['mer_no']){
            $this->error('当前状态无法驳回操作');
        }
        try{
            $data['merchant_status'] = 1;
            //驳回需重新签订合同
            $data['contract_id'] = 0;
            $data['agreement_status'] = 1;
            $data['reject_msg'] = $reject_msg;
            $payMerchant->allowField(true)->save($data);
        }catch(PDOException|Exception $e){
            $this->error('提交异常'.$e->getMessage());
        }
        $this->success('驳回成功，待商户重新提交审核');
    }


    /**
     * 发起进件，注册商户
     * @return void
     */
    public function incoming(){
        $id = $this->request->param('id');
        if(!$id){
            $this->error('缺少重要参数');
        }
        $payMerchant = model('\app\common\model\PayMerchant')->get($id);
        if(!$payMerchant){
            $this->error('未知信息');
        }
        if(!$payMerchant['contract_id'] || $payMerchant['agreement_status'] != 2){
            $this->error('尚未签署入网协议');
        }
        if($payMerchant['mer_no']){
            $this->error('不可重复进件');
        }
        $body = json_decode($payMerchant['body'],1);

        $merNameLen = mb_strlen($body['merName']);

        if($merNameLen < 4 || $merNameLen > 20){
            $this->error('商户简称要求：4~20字；小微可使用格式推荐：商户_法人姓名');
        }
        $lock_key = $this->request->action().$id;
        $this->serviceLock(60,$lock_key);
        try{
            $data = $this->regMerchant($body+['contractNo'=>model('\app\common\model\PayMerchantContract')->where('id',$payMerchant['contract_id'])->value('ec_no')],true);
            $data['merchant_status'] = 3;
            $payMerchant->allowField(true)->save($data);
        }catch(PDOException|Exception $e){
            $this->error('提交异常'.$e->getMessage());
        } finally {
            $this->unServiceLock($lock_key);
        }
        $this->success();
    }


    /**
     * 增终进件
     * @return void
     */
    public function addTerm(){
        $id = $this->request->param('id');
        $add_term = $this->request->param('add_term','WECHAT_PAY');//WECHAT_PAY : 专业化扫码
        if(!$id){
            $this->error('缺少重要参数');
        }
        $payMerchant = model('\app\common\model\PayMerchant')->get($id);
        if(!$payMerchant){
            $this->error('未知信息');
        }
        if($payMerchant['channel_status'] != 'OPEN'){
            $this->error('尚未开通，无法增终');
        }
        if($payMerchant['update_status'] == 'PASSING'){
            $this->error('变更信息审核中，待审核完成后操作');
        }
        if($payMerchant['add_term']){
            $this->error('不可重复增终进件');
        }
        try{
            $req = [
                'action'=>'addTerm',
                'type'=>2,
                'data'=>[
                    'mer_no'=>$payMerchant->mer_no,
                    'bzPos'=>$add_term,
                    'shop_no'=>$payMerchant->shop_no
                ],
            ];
            $res = Hook::listen('lakala',$req)[0];
            if($res['code'] == '000000'){
                $payMerchant->update_id = $res['data']['reviewRelatedId'];
                $payMerchant->update_status = 'PASSING';//审核中
                $payMerchant->add_term = $payMerchant->add_term ? $payMerchant->add_term.','.$add_term : $add_term;
                $payMerchant->save();
            }else{
                $this->error('增终异常'.$res['data']['message'],'',$res);
            }

        }catch(PDOException|Exception $e){
            $this->error('提交异常'.$e->getMessage());
        }
        $this->success();
    }

    /**
     * 商家进件
     * @param mixed $params
     * @return array|array{body: bool|string, channel: string}
     */
    protected function regMerchant($params,$reg = true){
        $data = [
            'channel'=>'LAKALA',
            'reg_name'=>$params['merRegName'],
            'name'=>$params['merName'],
            'larname'=>$params['larName'],
            'mobile'=>$params['contactMobile'],
        ];
        $params['address'] = $params['provinceName'].$params['cityName'].$params['countyName'].$params['merAddr'];
        //提交进件
        if($reg){
            $code = geocode($params['address']);

            if($code['code'] == 0){
                $params['latitude'] = $code['data'][0];
                $params['longtude'] = $code['data'][1];
            }

            $params['attchments'] = $this->uploadAttchments($params['images']);
            $req2 = [
                'action'=>'regMerchant',
                'type'=>2,
                'data'=>$params,
            ];
            $res2 = Hook::listen('lakala',$req2)[0];

            if(array_get($res2,'merchantNo')){
                $data['merchant_no'] = $res2['merchantNo'];
                $data['channel_status'] = array_get($res2,'status','');
            }else{
                $this->error($res2['message'],'',$res2);
            }
        }
        $data['body'] = json_encode($params,JSON_UNESCAPED_UNICODE);
        return $data;
    }


    /**
     * 上传附件
     * @param mixed $images
     * @return array{id: mixed, type: mixed[]}
     */
    protected function uploadAttchments($images,$imgPath = 'id',$imgType = 'type'){
        $attchments = [];
        foreach($images as $key => &$value){
            if($value){
                //将附件上传
                if (!preg_match("/^http/", $value)) {
                    $pre = 'https://dmnuo.oss-cn-hangzhou.aliyuncs.com';
                    $value = $pre.$value;
                    $headers = get_headers($value);
                    if(stripos($headers[0], '200') == false){
                        $value = str_replace($pre,'https://'.$_SERVER['HTTP_HOST'],$value);
                    }
                }
                $req = [
                    'action'=>'upload',
                    'type'=>2,
                    'data'=>[
                        'file'=>$value,
                        'imgType'=>$key,
                    ],
                ];
                $res = Hook::listen('lakala',$req)[0];
                if($res && array_get($res,'status') &&  $res['status'] != '02'){
                    $attchments[] = [
                        $imgPath=>$res['url'],
                        $imgType=>$key,
                    ];
                }else{
                    $this->error('附件上传异常','',$res);
                }
            }
        }
        return $attchments;
    }



    /**
     * 获取地区信息
     * @return \think\response\Json
     */
    public function getArea(){
        $parentCode = $this->request->param('parentCode',1);
        $params = [
            'action'=>'area',
            'type'=>2,
            'data'=>[
                'parentCode'=>$parentCode,
                'isBank'=>$this->request->param('isBank',false) ? true : false,
            ],
        ];
        $list = Hook::listen('lakala',$params)[0];
        if($parentCode == 3300){
            array_unshift($list,[
                'code'=>'3320',
                'createTime'=>'',
                'id'=>4174,
                'name'=>'宁波市',
                'optimistic'=>'宁波市',
                'parentCode'=>'3300',
                'updateTime'=>'',
            ]);
        }
        if($parentCode == 3320){
            $list = array_merge($list,[
                [
                    'code'=>'3327',
                    'createTime'=>'',
                    'id'=>3327,
                    'name'=>'海曙区',
                    'optimistic'=>'海曙区',
                    'parentCode'=>'3320',
                    'updateTime'=>'',
                ],
                [
                    'code'=>'3328',
                    'createTime'=>'',
                    'id'=>3328,
                    'name'=>'北仑区',
                    'optimistic'=>'北仑区',
                    'parentCode'=>'3320',
                    'updateTime'=>'',
                ],
                [
                    'code'=>'3329',
                    'createTime'=>'',
                    'id'=>3329,
                    'name'=>'镇海区',
                    'optimistic'=>'镇海区',
                    'parentCode'=>'3320',
                    'updateTime'=>'',
                ],
                [
                    'code'=>'3330',
                    'createTime'=>'',
                    'id'=>3330,
                    'name'=>'江北区',
                    'optimistic'=>'江北区',
                    'parentCode'=>'3320',
                    'updateTime'=>'',
                ],
            ]);
        }

        $searchKey = $this->request->param('searchKey');
        $searchValue = $this->request->param('searchValue');
        if($searchValue){
            foreach($list as $info){
                if($info[$searchKey] == $searchValue){
                    $list = [$info];
                    break;
                }
            }
        }
        return json(['list' => $list, 'total' => count($list)]);
    }


    /**
     * 获取银行信息
     * @return \think\response\Json
     */
    public function getBank(){
        $params = [
            'action'=>'bank',
            'type'=>2,
            'data'=>[
                'areaCode'=>$this->request->param('areaCode'),
                'bankName'=>$this->request->param('bankName'),
            ],
        ];
        $list = Hook::listen('lakala',$params)[0];
        $searchKey = $this->request->param('searchKey');
        $searchValue = $this->request->param('searchValue');
        if($searchValue){
            foreach($list as $info){
                if($info[$searchKey] == $searchValue){
                    $list = [$info];
                    break;
                }
            }
        }
        return json(['list' => $list, 'total' => count($list)]);
    }


    /**
     * 获取银行信息
     * @return \think\response\Json
     */
    public function getCategory(){
        $params = [
            'action'=>'category',
            'type'=>2,
            'data'=>[
                'parentCode'=>$this->request->param('parentCode'),
            ],
        ];
        $list = Hook::listen('lakala',$params)[0];
        $searchKey = $this->request->param('searchKey');
        $searchValue = $this->request->param('searchValue');
        if($searchValue){
            foreach($list as $info){
                if($info[$searchKey] == $searchValue){
                    $list = [$info];
                    break;
                }
            }
        }
        return json(['list' => $list, 'total' => count($list)]);
    }


    /**
     * 复议提交
     */
    public function reconsider(){
        $id = $this->request->param('id');
        if(!$id){
            $this->error('缺少参数');
        }
        $row = $this->model->get($id);
        if(!$row){
            $this->error('未知数据');
        }
        if(!$row['contract_id'] || $row['agreement_status'] != 2){
            $this->error('尚未签署入网协议');
        }
        if($row['mer_no']){
            $this->error('无需提交复议');
        }
        if($row['mer_no'] || $row->channel_status != 'REJECT'){
            $this->error('无需提交复议');
        }
        $params = [
            'action'=>'reconsider',
            'type'=>2,
            'data'=>[
                'merchant_no'=>$row['merchant_no'],
            ],
        ];
        $res = Hook::listen('lakala',$params)[0];
        if($res['code'] == '000000'){
            $row->channel_status = 'WAIT_AUDI';//待审核
            $row->merchant_status = '3';//待审核
            $row->save();
            $this->success($res['message'],'',$res);
        }
        $this->error($res['message'],'',$res);
    }



    /**
     * 查询商户信息
     */
    public function queryMerchant(){
        $id = $this->request->param('id');
        if(!$id){
            $this->error('缺少参数');
        }
        $row = $this->model->get($id);
        if(!$row){
            $this->error('未知数据');
        }
        $params = [
            'action'=>'queryMerchant',
            'type'=>2,
            'data'=>[
                'merchant_no'=>$row['merchant_no'],
                'mer_no'=>$row['mer_no'],
            ],
        ];
        $res = Hook::listen('lakala',$params)[0];
        if($res['code'] == '000000'){
            if($row){
                $row->reg_name = $res['data']['customer']['merRegName'];
                $row->name = $res['data']['customer']['customerName'];
                !$row->larname && $row->larname = $res['data']['customer']['legalName'];
                !$row->mobile && $row->mobile = $res['data']['customer']['phoneNo'];
                $row->channel_status = $res['data']['customer']['customerStatus'];
                $row->is_valid = 2;
                if($row->channel_status == 'OPEN'){
                    $row->mer_no = $res['data']['customer']['externalCustomerNo'];
                    $row->shop_no = $res['data']['shopInfoList'][0]['shopId'];
                    $row->term_info = json_encode($res['data']['terminalInfo'],JSON_UNESCAPED_UNICODE);
                    $row->is_valid = 1;
                    $row->merchant_status = 4;//已完成
                }
                // $row->agreement_status = $res['data']['customer']['agreementStatus'] == 'TRUE' ? 2 : 1;
                if($row->channel_status == 'REJECT'){
                    //被拒，自动转为待审核，并记录被拒原因
                    $row->reject_msg = $res['data']['customer']['auditRemark'];
                    $row->merchant_status = 1;//待提交
                    //驳回需重新签订合同
//                    $row->contract_id = 0;
//                    $row->agreement_status = 1;
                    $row->save();
                    $this->error($row->reject_msg,'',$res);
                }
                $row->save();
            }
        }else{
            $this->error($res['message'],'',$res);
        }
        if($this->request->param('show') == 2){
            $this->success('','',$res);
        }
        $this->view->assign('row', $res['data']);
        return $this->view->fetch('merchant');
    }


    /**
     * 查询子商户信息
     */
    public function querySubMerchant(){
        $id = $this->request->param('id');
        if(!$id){
            $this->error('缺少参数');
        }
        $row = $this->model->get($id);
        if(!$row){
            $this->error('未知数据');
        }
        $params = [
            'action'=>'queryMerchantNo',
            'type'=>2,
            'data'=>[
                'mer_no'=>$row['mer_no'],
            ],
        ];
        $res = Hook::listen('lakala',$params)[0];
        if($res['code'] == '000000'){
            $sub_info = [
                'wXList'=>[],
                'zFBList'=>[],
            ];
            $row->sub_info && $sub_info = json_decode($row->sub_info,1) + $sub_info;
            foreach($res['data'] as $type => &$infos){
                foreach($infos as &$v){
                    $v['auth'] = 2;//是否授权，1是，2否
                    foreach($sub_info[$type] as $vv){
                        if($v['channelId'] == $vv['channelId'] && $v['subMerchantNo'] == $vv['subMerchantNo']){
                            $v['auth'] = array_get($vv,'auth',2);
                        }
                        if($v['auth'] == 2){
                            $v['auth'] = $this->findAuth($type,$v['subMerchantNo'],$row->mer_no);
                        }
                    }
                }
            }
            $row->sub_info = json_encode($res['data'],JSON_UNESCAPED_UNICODE);
            $row->save();
            $this->view->assign('row', $res['data']);
            $this->view->assign('id', $id);
            return $this->view->fetch('submerchant');
        }
        $this->error($res['message'],'',$res);
    }

    /**
     * 返回认证状态
     * @param $type
     * @param $subMerchantId
     * @param $merchantNo
     * @return int
     */
    private function findAuth($type,$subMerchantId,$merchantNo){
        $type == 'wXList' ? $tradeMode = 'WECHAT' : $tradeMode = 'ALIPAY';
        $params = [
            'action'=>'mrchAuthStateQuery',
            'data'=>[
                'tradeMode'=>$tradeMode,
                'subMerchantId'=>$subMerchantId,
                'merchantNo'=>$merchantNo,
            ],
        ];
        $res = Hook::listen('lakala',$params)[0];
        $auth = 2;
        if($res['retCode'] == '000000'){
            if(($tradeMode == 'WECHAT' && $res['respData']['checkResult'] == 'AUTHORIZE_STATE_AUTHORIZED') || ($tradeMode == 'ALIPAY' && $res['respData']['checkResult'] == 'AUTHORIZED')){
                $auth = 1;//已授权
            }
        }else{
            $this->error($res['retMsg'],'',$res);
        }
        return $auth;
    }

    /**
     * 查询子商户授权信息
     */
    public function querySubMerchantAuth(){
        $id = $this->request->param('id');
        if(!$id){
            $this->error('缺少参数');
        }
        $row = $this->model->get($id);
        if(!$row){
            $this->error('未知数据');
        }

        $sub_info = json_decode($row->sub_info,1) ?? [];

        foreach($sub_info as $type => &$infos){
            foreach($infos as &$v){
                if($v['auth'] == 1){
                    continue;
                }
                $v['auth'] = $this->findAuth($type,$v['subMerchantNo'],$row->mer_no);
            }
        }
        $row->sub_info = json_encode($sub_info,JSON_UNESCAPED_UNICODE);
        $row->save();
        $this->success('同步成功');
    }

    /**
     * 查询变更状态
     */
    public function queryUpdate(){
        $id = $this->request->param('id');
        if(!$id){
            $this->error('缺少参数');
        }
        $row = $this->model->get($id);
        if(!$row){
            $this->error('未知数据');
        }
        if($row['update_status'] != 'PASSING'){
            $this->error('非待审核，无需查询');
        }
        if(!$row['update_id']){
            $this->error('缺少参数');
        }

        $params = [
            'action'=>'queryUpdate',
            'type'=>2,
            'data'=>[
                'update_id'=>$row['update_id'],
            ],
        ];
        $res = Hook::listen('lakala',$params)[0];
        if(array_get($res,'reviewPass')){
            $row->update_status = $res['reviewPass'];
            $row->update_msg = $res['reviewResult'] ?? '';
            $row->save();
        }else{
            $this->error('查询异常，请稍后再试','',$res);
        }
        $this->success($res['reviewResult'],'',$res);
    }

    /**
     * 申请合同
     */
    public function applyContract(){
        $id = $this->request->param('id');
        $again = $this->request->param('again');
        $ecTypeCode = $this->request->param('ecTypeCode','EC005');
        if(!$id){
            $this->error('缺少参数');
        }
        $row = $this->model->get($id);
        if(!$row){
            $this->error('未知数据');
        }
        $contractModel = model('\app\common\model\PayMerchantContract');
        $contract = $contractModel->get($row['contract_id']);
        //重新申请，或未申请过
        if(!$contract || $again){
            if($row['channel_status'] == 'OPEN'){
                $this->error('已开通无需申请合同','');
            }
            if($again && $row['agreement_status'] == 2){
                $this->error('入网协议已签署，无需重复申请合同','');
            }
            $params = [
                'action'=>'apply',
                'data'=>json_decode($row['body'],1)+['ecTypeCode'=>$ecTypeCode],
            ];
            $res = Hook::listen('lakala',$params)[0];
            if($res['retCode'] == '000000'){
                $contract = $contractModel;
                $contract->merchant_id = $id;
                $contract->mobile = $row['mobile'];
                $contract->type = $ecTypeCode;
                $contract->ec_no = '';
                $contract->ec_name = '';
                $contract->ec_status = '';
                $contract->order_no = $res['respData']['orderNo'];
                $contract->apply_id = $res['respData']['ecApplyId'];
                $contract->exp_time = $res['respData']['resultUrlExpTm'];
                $contract->result_url = $res['respData']['resultUrl'];
                $contract->notice = json_encode($res,JSON_UNESCAPED_UNICODE);
                $contract->save();
                $row->contract_id = $contract->id;
                $row->save();
            }else{
                $this->error($res['retMsg'],'',$res);
            }
        }
        if($again){
            $this->success('','',json_decode($contract->notice,1));
        }else{
            $this->view->assign('row', $contract);
            return $this->view->fetch('contract');
        }
    }


    /**
     * 账户余额
     */
    public function balanceQuery(){
        $id = $this->request->param('id');
        if(!$id){
            $this->error('缺少参数');
        }
        $row = $this->model->get($id);
        if(!$row){
            $this->error('未知数据');
        }
        $params = [
            'action'=>'balanceQuery',
            'data'=>[
                'mer_no'=>$row['mer_no']
            ],
        ];
        $balance = Hook::listen('lakala',$params)[0];
        if($balance['retCode'] == '000000'){
            $this->view->assign('row', $row);
            $this->view->assign('balance', $balance['respData']);
            return $this->view->fetch('balance');
        }else{
            $this->error($balance['retMsg'],'',$balance);
        }
    }
}
