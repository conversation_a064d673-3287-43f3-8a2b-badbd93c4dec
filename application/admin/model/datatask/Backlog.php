<?php

namespace app\admin\model\datatask;

use think\Model;


class Backlog extends Model
{

    // 表名
    protected $name = 'datatask_backlog';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;

    // 定义时间戳字段名
    protected $createTime = 'create_time';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'create_time_text'
    ];
    

    



    public function getCreateTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['create_time']) ? $data['create_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setCreateTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function datataskconfig()
    {
        return $this->belongsTo(Config::class, 'config_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
