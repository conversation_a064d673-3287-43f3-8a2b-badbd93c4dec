<?php

namespace app\admin\model\wanlshop;

use think\Model;


class Currencyfhq extends Model
{

    // 表名
    protected $name = 'user_currency_fhq_log';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'type_text'
    ];

    public function getTypeList()
    {
        return ['mem' => __('Type mem'), 'recharge' => __('Type recharge'), 'sys' => __('Type sys')];
    }

    public function getTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['type']) ? $data['type'] : '');
        $list = $this->getTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    public function user()
    {
        return $this->belongsTo('app\admin\model\User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    public function saas()
    {
        return $this->belongsTo('app\admin\model\Saas', 'saas_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
