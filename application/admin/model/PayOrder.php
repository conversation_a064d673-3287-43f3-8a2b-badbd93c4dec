<?php

namespace app\admin\model;

use think\Model;


class PayOrder extends Model
{

    

    

    // 表名
    protected $name = 'pay_order';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'paytime_text',
        'status_text'
    ];
    

    
    public function getStatusList()
    {
        return ['created' => __('Status created'), 'paid' => __('Status paid'), 'expired' => __('Status expired')];
    }

    public function getPaytypeList()
    {
        return [
            'CURRENCY_BD' => __('CURRENCY_BD'), 
            'CURRENCY_XFQ' => __('CURRENCY_XFQ'), 
            'CURRENCY_SXFQ' => __('CURRENCY_SXFQ'), 
            'ALIPAY' => __('ALIPAY'),
            'WECHAT' => __('WECHAT'),
            'LAKALA' => __('LAKALA'),
             'LAKALA_WECHAT' => __('LAKALA_WECHAT'),
             'LAKALA_ALIPAY' => __('LAKALA_ALIPAY'),
        ];
    }



    public function getPaytimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['paytime']) ? $data['paytime'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    protected function setPaytimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function shop()
    {
        return $this->belongsTo('app\admin\model\wanlshop\Shop', 'shop_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
