<?php
namespace app\admin\model;

use think\Model;

class DealerInfo extends Model
{

    // 表名
    protected $name = 'dealer_info';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'state_text',
        'verify_text'
    ];

    public function getStateList()
    {
        return ['0' => __('个人'), '1' => __('个体'), '2' => __('企业')];
    }
    
    public function getVerifyList()
    {
        return ['0' => __('等待审核'), '1' => __('通过'), '2' => __('未通过')];
    }


    public function getStateTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['state']) ? $data['state'] : '');
        $list = $this->getStateList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getVerifyTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['verify']) ? $data['verify'] : '');
        $list = $this->getVerifyList();
        return isset($list[$value]) ? $list[$value] : '';
    }
	
	public function user()
	{
	    return $this->belongsTo('app\common\model\User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
	}

}
